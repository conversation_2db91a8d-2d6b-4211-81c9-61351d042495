import { createSlice } from "@reduxjs/toolkit";

const packageSelectionSlice = createSlice({
  name: "packageSelection",
  initialState: {
    packageId: null,
    type: null, // 'package' hoặc 'credit'
  },
  reducers: {
    setPackageId: (state, action) => {
      state.packageId = action.payload;
    },
    clearPackageId: (state) => {
      state.packageId = null;
      state.type = null;
    },
    setType: (state, action) => {
      state.type = action.payload;
    },
  },
});

export const { setPackageId, clearPackageId, setType } = packageSelectionSlice.actions;
export default packageSelectionSlice.reducer;