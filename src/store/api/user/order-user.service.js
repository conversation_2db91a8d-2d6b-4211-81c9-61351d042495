import axios from 'axios';

class OrderService {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    // Tạo instance axios với config mặc định
    this.api = axios.create({
      baseURL: baseUrl,
      headers: {
        'Content-Type': 'application/json',
      }
    });
  }

  // Thêm method để set token
  setToken(token) {
    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  async createOrder(data) {
    try {
      const response = await this.api.post('/orders', data);
      return response.data;
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }

  async getOrders(idUser) {
    try {
      const response = await this.api.get(`/orders/user/${idUser}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  }

  async getOrderById(id) {
    try {
      const response = await this.api.get(`/orders/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching order ${id}:`, error);
      throw error;
    }
  }


}

const API_URL = import.meta.env.VITE_PAYMENT_DOMAIN || ""

// Khởi tạo service với URL
const orderService = new OrderService(API_URL);

// Lấy token từ localStorage hoặc nơi bạn lưu trữ token
const token = localStorage.getItem("token")
if (token) {
  orderService.setToken(token);
}

export default orderService; 