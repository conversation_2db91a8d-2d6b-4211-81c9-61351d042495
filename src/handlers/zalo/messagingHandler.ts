'use strict';
import { WORKGATE_TYPES, ZaloPersonalWorkGateConfig } from '../../entities/workGate.entity';
import { Service } from '../../types/moleculer';

/**
 * Handles Zalo OA messaging events
 */
export async function handleZaloMessagingEvents(
  data: any,
  broker: Service<{}>['broker'],
  logger: Service<{}>['logger'],
): Promise<void> {
  try {
    // Check if this is a valid Zalo message event
    if (!data || !data.event_name) {
      logger.warn('Webhook zalo: Invalid message format', data);
      return;
    }

    logger.info('Webhook zalo: Processing event', data.event_name);

    // Handle different event types
    switch (data.event_name) {
      case 'user_send_text':
        await handleUserSendText(data, broker, logger);
        break;
      case 'user_send_image':
        await handleUserSendImage(data, broker, logger);
        break;
      case 'user_send_file':
        await handleUserSendFile(data, broker, logger);
        break;
      case 'follow':
        await handleUserFollow(data, broker, logger);
        break;
      case 'unfollow':
        await handleUserUnfollow(data, broker, logger);
        break;
      default:
        logger.info(`Webhook zalo: Unhandled event type ${data.event_name}`, data);
        break;
    }
  } catch (error) {
    logger.error('Webhook zalo: Error processing message', error.message);
  }
}

/**
 * Handle text messages from users
 */
async function handleUserSendText(
  data: any,
  broker: Service<{}>['broker'],
  logger: Service<{}>['logger'],
): Promise<void> {
  const senderId = data.sender.id;
  const message = data.message.text;
  const timestamp = data.timestamp || Date.now();
  const oaId = data.recipient.id;
  const channel_id = 'zalo:' + oaId;
  const environment = 'prod';

  try {
    // Find the workgate for this Zalo OA
    const workGate = await broker
      .call('workGate.actionLocalGet', {
        query: {
          type: WORKGATE_TYPES.ZALO_PERSONAL,
          'gateConfig.uuid': oaId,
        },
      })
      .catch((_) => undefined);

    if (!workGate) {
      logger.warn('Webhook zalo: No workgate for Zalo OA', oaId, senderId);
      return;
    }

    // Cast the config to ZaloPersonalWorkGateConfig
    const config = workGate.config as ZaloPersonalWorkGateConfig;
    const on_chat_uuid = config.on_chat_bot_uuid;

    // Check if on_chat_uuid is set
    if (!on_chat_uuid) {
      logger.warn(`Webhook zalo: No on_chat_bot_uuid configured for workgate (${workGate._id})`);
      return;
    }

    logger.info(`Webhook zalo: Receive message (${workGate._id}): ${senderId}`, timestamp, message);

    // Save message to history
    // @ts-expect-error
    await broker.call('chatHistory.actionSaveSessionHistoryRecord', {
      bot_uuid: on_chat_uuid,
      thread_id: senderId,
      channel_id,
      environment,
      history: {
        _id: `zalo_${timestamp}`,
        message: message,
        timestamp: timestamp,
        sender: 'user',
      },
    });

    // Check workgate status - if inactive, only save message history
    if (workGate.status === 2) { // INACTIVE status
      logger.info(`Workgate ${workGate._id} is inactive, only saving message history`);
      return;
    }

    // Process the message with coreAi only if workgate is active
    const result = await broker.call('coreAi.actionProcessFlow', {
      request_body: {
        message: message,
        profile: {
          name: data.sender.name || '',
          avatar: data.sender.avatar || '',
        },
      },
      bot_uuid: on_chat_uuid,
      thread_id: senderId,
      channel_id,
      environment,
      timestamp,
    });

    if (result?.message) {
      logger.info(`Webhook zalo: Reply message (${workGate._id}): ${senderId}`, timestamp, result.message);

      // Send the response back to the user
      // This would typically call a Zalo API to send the message
      // For now, we'll just log it
      logger.info(`Webhook zalo: Would send message to ${senderId}: ${result.message}`);

      // If we have an active socket connection for this workgate, we can send the message through it
      await broker.call('socketConnector.sendClientTask', {
        workgate_id: workGate._id,
        task: {
          user: {
            userId: senderId,
            name: data.sender.name || '',
          },
          message: result.message,
          timestamp: Date.now(),
          zaId: oaId,
        },
      }).catch(error => {
        logger.error(`Webhook zalo: Error sending message via socket (${workGate._id}): ${senderId}`, error.message);
      });
    }
  } catch (error) {
    logger.error(`Webhook zalo: Error processing text message ${channel_id}:${senderId}`, error.message);
  }
}

/**
 * Handle image messages from users
 */
async function handleUserSendImage(
  data: any,
  broker: Service<{}>['broker'],
  logger: Service<{}>['logger'],
): Promise<void> {
  // Similar implementation to handleUserSendText but for images
  logger.info('Webhook zalo: Received image message', data.sender.id);
  // For now, we'll just acknowledge that we received an image
  // In a full implementation, you would download the image and process it
}

/**
 * Handle file messages from users
 */
async function handleUserSendFile(
  data: any,
  broker: Service<{}>['broker'],
  logger: Service<{}>['logger'],
): Promise<void> {
  // Similar implementation to handleUserSendText but for files
  logger.info('Webhook zalo: Received file message', data.sender.id);
  // For now, we'll just acknowledge that we received a file
  // In a full implementation, you would download the file and process it
}

/**
 * Handle user follow events
 */
async function handleUserFollow(
  data: any,
  broker: Service<{}>['broker'],
  logger: Service<{}>['logger'],
): Promise<void> {
  logger.info('Webhook zalo: User followed', data.sender.id);
  // Handle follow event - perhaps send a welcome message
}

/**
 * Handle user unfollow events
 */
async function handleUserUnfollow(
  data: any,
  broker: Service<{}>['broker'],
  logger: Service<{}>['logger'],
): Promise<void> {
  logger.info('Webhook zalo: User unfollowed', data.sender.id);
  // Handle unfollow event - perhaps update user status in your system
}
