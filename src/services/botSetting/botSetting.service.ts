'use strict';
import { getModelForClass } from '@typegoose/typegoose';
import _ from 'lodash';
import { Action, Event, Method, Service as DService } from 'moleculer-decorators';
import * as MoleculerTs from 'moleculer-ts';
import { RequireRoles } from '../../commons/annotation.helper';
import { CommonConfig } from '../../commons/common.config';
import { CommonErrors } from '../../commons/error.helper';
import { IBotSetting, SNAPSHOT_TYPES, TEMPLATE_TYPES } from '../../entities/botSetting.entity';
import { DbBaseMixin, DbBaseMixinActionTypes } from '../../mixins';
import { BotSetting, BotSettingSchema, BotSettingType } from '../../models/botSetting';
import { ApiGatewayMeta, BotSettingServiceTypes, MoleculerDBService } from '../../types';
import { Context, eventName } from '../../types/moleculer';
import { convertPaginateOptions, convertPaginateQuery } from '../../utils/mongoPlugins/paginate';
import { OwnActions } from './botSetting.service.types';

import { ReadStream } from 'fs';
import path from 'path';
import { checkAndCleanBotConfig } from '../../commons/flow.helper';
import { USER_ROLE } from '../../entities/user.entity';
import { FileStreamMeta, getPromiseFileSteam } from '../../utils/FileUploadUtils';

const dbBaseMixin = new DbBaseMixin({
  dbUri: CommonConfig.DB_URI,
  name: 'dbBotSettingMixin',
  collection: 'botSetting',
  model: getModelForClass(BotSetting),
});
const allowUpdateFields = _.pick(BotSettingSchema, [
  '_id',
  'name',
  'desc',
  // 'team_id',
  'status',
  'config',
  'copyright',
  'greeting',
  'question_1',
  'question_2',
  'question_3',
  'greeting',
  'max_history',
  'max_history_day',
  'logo_url',
  'live_config',
]);
Object.keys(allowUpdateFields).forEach((key) => {
  // @ts-ignore
  allowUpdateFields[key].optional = true;
});
allowUpdateFields['config'] = { type: 'object', optional: true };
allowUpdateFields['live_config'] = {
  type: 'object',
  optional: true,
};
// @ts-ignore
allowUpdateFields['$$strict'] = true;

@DService({
  name: BotSettingServiceTypes.name,
  mixins: [dbBaseMixin.getMixin(async (adapter) => {}, [])],
  settings: {
    fields: [
      '_id',
      'name',
      'desc',
      'type',
      'team_id',
      'status',
      'config',
      'copyright',
      'greeting',
      'question_1',
      'question_2',
      'question_3',
      'max_history',
      'max_history_day',
      'logo_url',
      'template_type',
    ],
    listFields: [
      '_id',
      'name',
      'desc',
      'type',
      // 'team_id',
      'status',
      // 'config',
      'logo_url',
      'copyright',
      'greeting',
    ],
  },
})
class BotSettingService
  extends MoleculerDBService<
    {
      fields: string[];
      listFields: string[];
    },
    BotSettingType
  >
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes>
{
  @RequireRoles()
  @Action({
    rest: 'POST /list',
    cache: {
      keys: ['#user.teamId', 'search', 'sort', 'page', 'pageSize'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true },
      pageSize: { type: 'number', integer: true, min: 0, optional: true, convert: true },
      sort: { type: 'string', optional: true },
      search: { type: 'string', optional: true },
    },
  })
  async actionList(
    ctx: Context<BotSettingServiceTypes.ActionParams<'actionList'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'actionList'>> {
    const query = convertPaginateQuery({ searchFields: ['name'], ...ctx.params });

    // Add additional query
    query.snapshot_type = SNAPSHOT_TYPES.ORIGINAL;
    query.team_id = ctx.meta.user.teamId;

    return this.adapter.model.paginate(
      query,
      convertPaginateOptions(
        { ...ctx.params, fields: this.settings.listFields },
        this.adapter.model.defaultPaginateOptions,
      ),
    );
  }

  @RequireRoles()
  @Action({
    rest: 'POST /create',
    params: {
      ..._.omit(BotSettingSchema, [
        '_id',
        'status',
        'team_id',
        'snapshot_type',
        'snapshot_name',
        'snapshot_id',
        '$$strict',
      ]),
      config: {
        type: 'object',
        properties: {
          sequence: { type: 'array', items: { type: 'object' } },
          properties: { type: 'object', optional: true },
        },
        required: ['sequence'],
        additionalProperties: false,
      },
      // config: { type: 'object', optional: false },
    },
    openapi: {
      description: 'Create new bot setting',
    },
  })
  async actionCreate(
    ctx: Context<BotSettingServiceTypes.ActionParams<'actionCreate'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'actionCreate'>> {
    // Validate type
    const type = ctx.params.type || 'chatbot';
    if (!['chatbot', 'flow'].includes(type)) {
      throw new CommonErrors.BadRequestError('Invalid type. Must be "chatbot" or "flow"');
    }

    // Get current team package to check bot setting limit
    const teamPackage = await this.broker.call('teamPackage.actionGetCurrent', {}, { parentCtx: ctx });
    if (!teamPackage) {
      throw new CommonErrors.NotFoundError('No active package found for team. Please upgrade your subscription!');
    }

    // Check bot setting limit
    const currentBotSettings = await this.adapter.model.countDocuments({
      team_id: ctx.meta.user.teamId,
      snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
    });

    if (currentBotSettings >= (teamPackage.botSettingLimit || 0)) {
      throw new CommonErrors.ValidationError(
        `Team has reached bot setting limit ${currentBotSettings}/${teamPackage.botSettingLimit}! Please upgrade your package to increase bot setting limit`,
      );
    }

    const config = ctx.params.config;
    const [cleanConfig, errorConfigs] = await checkAndCleanBotConfig(config, {
      type,
      pythonBlockToJS: async (script) => {
        const result = await this.broker.call('coreAi.actionGetOpenAiChat', {
          instruction: `convert this python to js:
Note:
+ if {{ variable }} convert to variable.
+ Do not use const, let, var when declare variable.
+ Output only code block, now explain or anything else

${script}`,
          message: '',
          chatHistories: [],
          modelName: 'gpt-4o-mini',
        });
        let output = result.message;
        // Replace all ```js or ```javascript or ```
        output = output?.replace(/^```(js|javascript|)/, '');
        if (output?.endsWith('\n```')) {
          output = output.substring(0, output.length - 4);
        }
        console.log('Convert python to js block:\n', script, '\n\n', output);
        return output;
      },
    });
    if (errorConfigs.length > 0) {
      throw new CommonErrors.UnprocessableError(errorConfigs);
    }

    delete ctx.params._id;
    const data: IBotSetting = {
      ...ctx.params,
      config: cleanConfig,
      // Set default value
      team_id: ctx.meta.user.teamId as string,
      type,
      status: 0,
      snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
    };
    const result = await this.adapter.insert(data);
    const doc = (await this.transformDocuments(ctx, {}, result)) as IBotSetting;
    this.broker.broadcast('botSetting.createdBotSetting', doc);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'PUT /update',
    params: allowUpdateFields,
  })
  async actionUpdate(
    ctx: Context<BotSettingServiceTypes.ActionParams<'actionUpdate'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'actionUpdate'>> {
    let data = await this.adapter.model.findOne({
      _id: ctx.params._id,
      team_id: ctx.meta.user.teamId,
      snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
    });
    if (!data) {
      throw new CommonErrors.NotFoundError('BotSetting not found');
    }
    data = _.merge(data, ctx.params);
    if (ctx.params.live_config) {
      data.markModified('live_config');
    }
    if (ctx.params.config) {
      const [cleanConfig, errorConfigs] = await checkAndCleanBotConfig(ctx.params.config, {
        type: data.type,
        pythonBlockToJS: async (script) => {
          const result = await this.broker.call('coreAi.actionGetOpenAiChat', {
            instruction: `convert this python to js:
Note:
+ if {{ variable }} convert to variable.
+ Do not use const, let, var when declare variable.
+ Output only code block, now explain or anything else

${script}`,
            message: '',
            chatHistories: [],
            modelName: 'gpt-4o-mini',
          });
          let output = result.message;
          // Replace all ```js or ```javascript or ```
          output = output?.replace(/^```(js|javascript|)/, '');
          if (output?.endsWith('\n```')) {
            output = output.substring(0, output.length - 4);
          }
          console.log('Convert python to js block:\n', script, '\n\n', output);
          return output;
        },
      });
      if (errorConfigs.length > 0) {
        throw new CommonErrors.UnprocessableError(errorConfigs);
      }
      data.config = cleanConfig;
    }
    await data.save();

    const doc = (await this.transformDocuments(ctx, {}, data)) as IBotSetting;
    doc.live_config = data.live_config;
    this.broker.broadcast('botSetting.updatedBotSetting', doc);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'GET /get',
    cache: {
      keys: ['#user.teamId', '_id'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
    },
  })
  async actionGet(
    ctx: Context<BotSettingServiceTypes.ActionParams<'actionGet'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'actionGet'>> {
    const data = await this.adapter.model
      .findOne({
        _id: ctx.params._id,
        team_id: ctx.meta.user.teamId,
        snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
      })
      .select(this.settings.fields)
      .lean({ parseId: true });

    if (!data) {
      throw new CommonErrors.NotFoundError('BotSetting not found');
    }
    return data;
  }

  /**
   * Only use for local api, and not check user permission
   */
  @Action({
    cache: {
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
      environment: { type: 'string', enum: ['dev', 'prod'], optional: false },
    },
  })
  async actionLocalGet(
    ctx: Context<BotSettingServiceTypes.ActionParams<'actionLocalGet'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'actionLocalGet'>> {
    const { _id, environment } = ctx.params;

    // For dev environment, get the original bot setting
    if (environment === 'dev') {
      const data = await this.adapter.model
        .findOne({
          _id,
          snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
        })
        .lean({ parseId: true });

      if (!data) {
        throw new CommonErrors.NotFoundError('BotSetting not found');
      }
      return data;
    }

    // For prod environment, get the production snapshot
    const prodSnapshot = await this.adapter.model
      .findOne({
        snapshot_id: _id,
        snapshot_type: 2, // Production snapshot
      })
      .lean({ parseId: true });

    if (!prodSnapshot) {
      throw new CommonErrors.NotFoundError('BotSetting not found');
    }

    return prodSnapshot;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /delete',
    params: {
      $$strict: true,
      _id: { type: 'string', required: true },
    },
  })
  async actionDelete(
    ctx: Context<BotSettingServiceTypes.ActionParams<'actionDelete'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'actionDelete'>> {
    const doc = await this.adapter.model
      .findOne({
        _id: ctx.params._id,
        team_id: ctx.meta.user.teamId,
        snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
      })
      .select(this.settings.fields)
      .lean({ parseId: true });
    if (!doc) {
      throw new CommonErrors.NotFoundError('BotSetting not found');
    }
    // Remove bot setting
    await this.adapter.removeMany({ _id: ctx.params._id });

    // Remove all snapshot
    await this.adapter.model.deleteMany({ snapshot_id: ctx.params._id });

    this.broker.broadcast('botSetting.removedBotSetting', doc);
    return true;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /snapshot',
    params: {
      bot_id: { type: 'string', required: true, empty: false },
      snapshot_name: { type: 'string', required: true, empty: false },
    },
  })
  async createSnapshot(
    ctx: Context<BotSettingServiceTypes.ActionParams<'createSnapshot'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'createSnapshot'>> {
    const { bot_id, snapshot_name } = ctx.params;

    // Get original bot setting and verify team_id
    const original = await this.adapter.model
      .findOne({
        _id: bot_id,
        team_id: ctx.meta.user.teamId,
        snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
      })
      .lean({ parseId: true });
    if (!original) {
      throw new CommonErrors.NotFoundError('Bot setting not found');
    }

    // Create snapshot
    const snapshot = { ...original };
    delete snapshot._id;
    snapshot.snapshot_type = SNAPSHOT_TYPES.SNAPSHOT;
    snapshot.snapshot_name = snapshot_name;
    snapshot.snapshot_id = bot_id;

    const result = await this.adapter.insert(snapshot);
    const doc = await this.adapter.model
      .findOne(
        { _id: result._id },
        {
          _id: 1,
          snapshot_id: 1,
          snapshot_type: 1,
          snapshot_name: 1,
          updated: 1,
        },
      )
      .lean({ parseId: true });

    this.broker.broadcast('botSetting.createdBotSetting', result);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /snapshot/restore',
    params: {
      snapshot_id: { type: 'string', required: true, empty: false },
      bot_uuid: { type: 'string', required: true, empty: false },
    },
  })
  async restoreFromSnapshot(
    ctx: Context<BotSettingServiceTypes.ActionParams<'restoreFromSnapshot'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'restoreFromSnapshot'>> {
    const { snapshot_id, bot_uuid } = ctx.params;

    // Get original bot setting and verify team_id
    const original = await this.adapter.model
      .findOne({
        _id: snapshot_id,
        team_id: ctx.meta.user.teamId,
        snapshot_id: bot_uuid,
      })
      .lean({ parseId: true });
    if (!original) {
      throw new CommonErrors.NotFoundError('Snapshot not found');
    }

    // Update the original bot setting
    const data = await this.adapter.model.findByIdAndUpdate(
      { _id: original.snapshot_id },
      {
        $set: _.omit(original, ['_id', 'snapshot_id', 'snapshot_type', 'snapshot_name']),
      },
      { new: true },
    );

    const doc = (await this.transformDocuments(ctx, {}, data)) as IBotSetting;
    this.broker.broadcast('botSetting.updatedBotSetting', doc);

    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'GET /snapshots/:bot_id',
    params: {
      bot_id: { type: 'string' },
    },
  })
  async listSnapshots(
    ctx: Context<BotSettingServiceTypes.ActionParams<'listSnapshots'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'listSnapshots'>> {
    const { bot_id } = ctx.params;

    // First verify if the bot setting belongs to the team
    const original = await this.adapter.model
      .findOne({
        _id: bot_id,
        team_id: ctx.meta.user.teamId,
        snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
      })
      .lean({ parseId: true });
    if (!original) {
      throw new CommonErrors.NotFoundError('Bot setting not found');
    }

    const snapshots = await this.adapter.model
      .find(
        {
          snapshot_id: bot_id,
          snapshot_type: { $in: [SNAPSHOT_TYPES.SNAPSHOT, SNAPSHOT_TYPES.PRODUCTION] },
        },
        {
          _id: 1,
          snapshot_id: 1,
          snapshot_type: 1,
          snapshot_name: 1,
          updated: 1,
        },
      )
      .sort('-updated')
      .lean({ parseId: true });

    return snapshots;
  }

  @RequireRoles()
  @Action({
    rest: 'DELETE /snapshot/:snapshot_id',
    params: {
      snapshot_id: { type: 'string' },
    },
  })
  async deleteSnapshot(
    ctx: Context<BotSettingServiceTypes.ActionParams<'deleteSnapshot'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'deleteSnapshot'>> {
    const { snapshot_id } = ctx.params;

    // Get snapshot and verify team_id
    const snapshot = await this.adapter.model
      .findOne({ _id: snapshot_id, team_id: ctx.meta.user.teamId })
      .select('_id snapshot_type snapshot_id team_id')
      .lean({ parseId: true });
    if (!snapshot) {
      throw new CommonErrors.NotFoundError('Snapshot not found');
    }
    // Only delete snapshot (not original and production)
    if (snapshot.snapshot_type !== SNAPSHOT_TYPES.SNAPSHOT && snapshot.snapshot_type !== SNAPSHOT_TYPES.PRODUCTION) {
      throw new CommonErrors.NotFoundError('Cannot delete non-snapshot bot setting');
    }

    await this.adapter.removeById(snapshot_id);
    this.broker.broadcast('botSetting.removedBotSetting', snapshot);

    // Notify to original if this is production snapshot
    if (snapshot.snapshot_type === (SNAPSHOT_TYPES.PRODUCTION as number)) {
      this.broker.broadcast('botSetting.removedBotSetting', {
        ...snapshot,
        snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
        _id: snapshot.snapshot_id,
      });
    }
    return true;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /publish',
    params: {
      bot_id: { type: 'string' },
    },
  })
  async publish(
    ctx: Context<BotSettingServiceTypes.ActionParams<'publish'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'publish'>> {
    const { bot_id } = ctx.params;

    // Get original bot setting and verify team_id
    const original = await this.adapter.model
      .findOne({
        _id: bot_id,
        team_id: ctx.meta.user.teamId,
        snapshot_type: 0,
      })
      .lean({ parseId: true });
    if (!original) {
      throw new CommonErrors.NotFoundError('Bot setting not found');
    }

    // Remove existing production version if exists
    await this.adapter.removeMany({
      snapshot_type: 2,
      snapshot_id: bot_id,
      team_id: ctx.meta.user.teamId, // Ensure we only delete production versions from this team
    });

    // Create production snapshot
    const snapshot = { ...original };
    delete snapshot._id;
    snapshot.snapshot_type = SNAPSHOT_TYPES.PRODUCTION;
    snapshot.snapshot_name = 'prod';
    snapshot.snapshot_id = bot_id;

    const result = await this.adapter.insert(snapshot);
    const doc = (await this.transformDocuments(ctx, {}, result)) as BotSetting;
    this.broker.broadcast('botSetting.createdBotSetting', doc);
    return {
      _id: result._id,
      snapshot_id: result.snapshot_id,
      snapshot_type: result.snapshot_type,
      snapshot_name: result.snapshot_name,
      updated: result.updated,
    };
  }

  @Method
  @Event({
    name: eventName('botSetting.createdBotSetting'),
  })
  cleanCacheOnCreate(botSetting: IBotSetting) {
    this.cleanCache(botSetting);
  }

  @Method
  @Event({
    name: eventName('botSetting.updatedBotSetting'),
  })
  cleanCacheOnUpdate(botSetting: IBotSetting) {
    this.cleanCache(botSetting);
  }

  @Method
  @Event({
    name: eventName('botSetting.removedBotSetting'),
  })
  cleanCacheOnRemove(botSetting: IBotSetting) {
    this.cleanCache(botSetting);
  }

  private cleanCache(botSetting: IBotSetting) {
    const meta = { user: { teamId: botSetting.team_id } };

    // Always clean these caches
    const cacheKeys = [
      // List cache
      this.broker.cacher.getCacheKey('botSetting.actionList', { search: '*' }, meta, ['#user.teamId', 'search']),

      // Get single item cache
      this.broker.cacher.getCacheKey('botSetting.actionGet', { _id: botSetting._id }, meta, ['#user.teamId', '_id']),

      // Live config cache
      this.broker.cacher.getCacheKey('botSetting.getLiveConfig', { bot_id: botSetting._id }, meta, [
        '#user.teamId',
        'bot_id',
      ]),

      // All names cache
      this.broker.cacher.getCacheKey('botSetting.getAllName', {}, meta, ['#user.teamId']),

      // Local get cache (no team isolation)
      this.broker.cacher.getCacheKey('botSetting.actionLocalGet', { _id: botSetting._id, environment: '*' }, {}, null),
    ];

    // Only clean template list cache if this is a template
    if (botSetting.template_type !== TEMPLATE_TYPES.DEFAULT) {
      cacheKeys.push(this.broker.cacher.getCacheKey('botSetting.getListTemplate', { type: '*' }, {}, ['type']));
    }

    // Clean only the specific keys
    this.broker.cacher.clean(cacheKeys);
  }

  @RequireRoles()
  @Action({
    rest: 'PUT /updateLogo/:bot_id',
    openapi: {
      description: 'Update bot logo by uploading an image file',
      requestBody: {
        content: {
          'multipart/form-data': {
            schema: {
              type: 'object',
              properties: {
                file: {
                  type: 'string',
                  format: 'binary',
                  description: 'The image file to upload',
                },
              },
            },
          },
        },
        required: true,
      },
    },
  })
  async updateLogo(
    ctx: Context<ReadStream, FileStreamMeta<{ bot_id: string }> & ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'updateLogo'>> {
    const { bot_id } = ctx.meta.$params;

    // Validate file type
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedMimeTypes.includes(ctx.meta.mimetype)) {
      ctx.params.destroy();
      throw new CommonErrors.BadRequestError('Only JPEG, PNG, GIF and WEBP images are allowed');
    }

    // Check bot setting exists
    const botSetting = await this.adapter.model.findOne({ _id: bot_id, team_id: ctx.meta.user.teamId });
    if (!botSetting) {
      ctx.params.destroy();
      throw new CommonErrors.NotFoundError('Bot setting not found');
    }

    // Save file to public/botLogo directory
    const extension = ctx.meta.mimetype.split('/')[1];
    const fileName = `bot_${bot_id}.${extension}`;
    const filePath = path.join('public', 'botLogo', fileName);

    const { filePath: savedPath } = await getPromiseFileSteam(
      ctx.params,
      filePath,
      500 * 1024, // 500KB limit
    );

    // Update bot setting with new logo URL
    const logoUrl = `/botLogo/${fileName}`;
    const updatedSetting = await this.adapter.model.findOneAndUpdate(
      { _id: bot_id, team_id: ctx.meta.user.teamId },
      { logo_url: logoUrl },
      { new: true },
    );

    const doc = (await this.transformDocuments(ctx, {}, updatedSetting)) as IBotSetting;
    this.broker.broadcast('botSetting.updatedBotSetting', doc);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'GET /getLiveConfig/:bot_id',
    cache: {
      keys: ['#user.teamId', 'bot_id'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      bot_id: { type: 'string', required: true },
    },
    openapi: {
      description: 'Get live config including logo URL and live chat config',
    },
  })
  async getLiveConfig(
    ctx: Context<BotSettingServiceTypes.ActionParams<'getLiveConfig'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'getLiveConfig'>> {
    const { bot_id } = ctx.params;

    const botSetting = await this.adapter.model
      .findOne(
        {
          _id: bot_id,
          team_id: ctx.meta.user.teamId,
          snapshot_type: SNAPSHOT_TYPES.ORIGINAL, // Only get from original bot setting
        },
        {
          greeting: 1,
          question_1: 1,
          question_2: 1,
          question_3: 1,
          copyright: 1,
          logo_url: 1,
          live_config: 1,
        },
      )
      .lean({ parseId: true });

    if (!botSetting) {
      throw new CommonErrors.NotFoundError('Bot setting not found');
    }

    return {
      logo_url: botSetting.logo_url || '',
      live_config: botSetting.live_config || {},
    };
  }

  @RequireRoles()
  @Action({
    rest: 'GET /getAllName',
    cache: {
      keys: ['#user.teamId'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
    },
  })
  async getAllName(
    ctx: Context<BotSettingServiceTypes.ActionParams<'getAllName'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'getAllName'>> {
    const team_id = ctx.meta.user.teamId;

    const botSettings = await this.adapter.model
      .find({ team_id, snapshot_type: SNAPSHOT_TYPES.ORIGINAL }, { _id: 1, name: 1 })
      .sort({ created: -1 })
      .lean({ parseId: true });

    return botSettings;
  }

  dependencies: ['api', 'coreAi'];

  @RequireRoles([USER_ROLE.ADMIN, USER_ROLE.MODERATOR])
  @Action({
    rest: 'POST /template/convert',
    openapi: {
      description: 'Convert a bot setting to a template. Template type: 0: Normal, 1: Public template',
    },
    params: {
      bot_id: { type: 'string', required: true },
      template_type: { type: 'number', enum: Object.values(TEMPLATE_TYPES), required: true },
    },
  })
  async convertToTemplate(
    ctx: Context<BotSettingServiceTypes.ActionParams<'convertToTemplate'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'convertToTemplate'>> {
    const { bot_id, template_type } = ctx.params;

    // Get and verify bot setting
    const botSetting = await this.adapter.model.findOne({
      _id: bot_id,
      team_id: ctx.meta.user.teamId,
      snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
    });
    if (!botSetting) {
      throw new CommonErrors.NotFoundError('Bot setting not found');
    }

    // Update template type
    botSetting.template_type = template_type;
    await botSetting.save();

    // Directly clean template list cache
    this.broker.cacher.clean([
      this.broker.cacher.getCacheKey('botSetting.getListTemplate', { search: '*' }, {}, ['search']),
    ]);

    const doc = (await this.transformDocuments(ctx, {}, botSetting)) as IBotSetting;
    this.broker.broadcast('botSetting.updatedBotSetting', doc);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /template/clone',
    openapi: {
      description: 'Create a new bot setting by cloning from a template',
    },
    params: {
      template_id: { type: 'string', required: true },
      name: { type: 'string', required: true },
    },
  })
  async cloneFromTemplate(
    ctx: Context<BotSettingServiceTypes.ActionParams<'cloneFromTemplate'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'cloneFromTemplate'>> {
    const { template_id, name } = ctx.params;

    // Get template
    const template = await this.adapter.model.findOne({
      _id: template_id,
      template_type: TEMPLATE_TYPES.PUBLIC_TEMPLATE,
    });
    if (!template) {
      throw new CommonErrors.NotFoundError('Template not found');
    }

    // Create new bot setting from template
    const newBotSetting: IBotSetting = {
      ...template.toObject(),
      _id: undefined,
      name,
      team_id: ctx.meta.user.teamId as string,
      template_type: TEMPLATE_TYPES.DEFAULT,
      snapshot_type: SNAPSHOT_TYPES.ORIGINAL,
    };

    const result = await this.adapter.insert(newBotSetting);
    const doc = (await this.transformDocuments(ctx, {}, result)) as IBotSetting;
    this.broker.broadcast('botSetting.createdBotSetting', doc);
    return doc;
  }

  @Action({
    rest: 'POST /template/list',
    openapi: {
      description: 'List of available templates',
    },
    cache: {
      keys: ['type', 'search', 'sort', 'page', 'pageSize'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true },
      pageSize: { type: 'number', integer: true, min: 0, optional: true, convert: true },
      sort: { type: 'string', optional: true },
      search: { type: 'string', optional: true },
      type: { type: 'string', enum: ['', 'flow', 'chatbot'], optional: true },
    },
  })
  async getListTemplate(
    ctx: Context<BotSettingServiceTypes.ActionParams<'getListTemplate'>, ApiGatewayMeta>,
  ): Promise<BotSettingServiceTypes.ActionReturn<'getListTemplate'>> {
    const query = convertPaginateQuery({ searchFields: ['name'], ...ctx.params });

    // Only get public templates
    query.template_type = TEMPLATE_TYPES.PUBLIC_TEMPLATE;
    query.snapshot_type = SNAPSHOT_TYPES.ORIGINAL;

    // Add type filter if provided
    if (ctx.params.type) {
      query.type = ctx.params.type;
    }

    return this.adapter.model.paginate(
      query,
      convertPaginateOptions(
        { ...ctx.params, fields: this.settings.listFields },
        this.adapter.model.defaultPaginateOptions,
      ),
    );
  }

  async started() {
    // this.waitForServices(['api']);
  }
}

export = BotSettingService;
