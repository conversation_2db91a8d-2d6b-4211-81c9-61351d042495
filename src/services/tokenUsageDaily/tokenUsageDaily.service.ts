'use strict';
import { Context } from '../../types/moleculer';
import { ApiGatewayMeta, ConfigServiceTypes, MoleculerDBService, TokenUsageDailyServiceTypes } from '../../types';
import { Action, Service as DService } from 'moleculer-decorators';
import { DbBaseMixin, DbBaseMixinActionTypes } from '../../mixins';
import { CommonConfig } from '../../commons/common.config';
import { getModelForClass } from '@typegoose/typegoose';
import * as MoleculerTs from 'moleculer-ts';
import { OwnActions } from './tokenUsageDaily.service.types';
import ConfigMixin from '../../mixins/config.mixin';
import { TokenUsageDaily, TokenUsageDailyType } from '../../models/tokenUsageDaily';
import { HourlyTokenUsage } from '../../entities/tokenUsageDaily.entity';
import { RequireRoles } from '../../commons/annotation.helper';
import { CommonErrors } from '../../commons/error.helper';

const dbBaseMixin = new DbBaseMixin({
  dbUri: CommonConfig.DB_URI,
  name: 'dbTokenUsageDailyMixin',
  collection: 'tokenUsageDaily',
  model: getModelForClass(TokenUsageDaily),
});

@DService({
  name: TokenUsageDailyServiceTypes.name,
  mixins: [ConfigMixin(['tokenUsageDaily.**']), dbBaseMixin.getMixin(async (adapter) => {}, [])],
  settings: {
    fields: ['_id', 'bot_uuid', 'date', 'hourlyUsage'],
  },
})
class TokenUsageDailyService
  extends MoleculerDBService<
    {
      fields: string[];
    },
    TokenUsageDailyType
  >
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes>
{
  private initializeHourlyTokenUsage(): HourlyTokenUsage {
    return {
      messages: 0,
      promptTokens: 0,
      evalTokens: 0,
      cost: 0,
    };
  }

  @Action({
    params: {
      bot_uuid: { type: 'string', required: true },
      prompt_token: { type: 'number', required: true },
      eval_token: { type: 'number', required: true },
      cost: { type: 'number', required: true },
    },
  })
  async addTokenUsage(
    ctx: Context<TokenUsageDailyServiceTypes.ActionParams<'addTokenUsage'>, ApiGatewayMeta>,
  ): Promise<TokenUsageDailyServiceTypes.ActionReturn<'addTokenUsage'>> {
    const { bot_uuid, prompt_token, eval_token, cost } = ctx.params;
    this.broker.logger.info('User spent token:', bot_uuid, ctx.params);
    const currentDate = new Date();
    const year = currentDate.getUTCFullYear();
    const month = currentDate.getUTCMonth() + 1;
    const day = currentDate.getUTCDate();
    const hour = currentDate.getUTCHours();
    const formattedDate = new Date(Date.UTC(year, month - 1, day));

    let tokenUsageDaily = await this.adapter.model.findOne({ bot_uuid, date: formattedDate }).lean({ parseId: true });
    if (!tokenUsageDaily) {
      // @ts-expect-error
      tokenUsageDaily = {
        bot_uuid,
        date: formattedDate,
        hourlyUsage: {},
      };
    }

    if (!tokenUsageDaily.hourlyUsage[hour]) {
      tokenUsageDaily.hourlyUsage[hour] = this.initializeHourlyTokenUsage();
    }

    tokenUsageDaily.hourlyUsage[hour].messages += 1;
    tokenUsageDaily.hourlyUsage[hour].promptTokens += prompt_token;
    tokenUsageDaily.hourlyUsage[hour].evalTokens += eval_token;
    tokenUsageDaily.hourlyUsage[hour].cost += cost;

    await this.adapter.model.updateOne({ bot_uuid, date: formattedDate }, tokenUsageDaily, { upsert: true });

    return tokenUsageDaily;
  }

  async configChanged(payload: ConfigServiceTypes.EventParams<'changed'>) {
    console.log('TokenUsageDaily event configChanged', payload);
  }

  created() {
    this.waitForServices(['api']);
  }

  @RequireRoles()
  @Action({
    rest: 'POST /reportUsage',
    params: {
      bot_uuid: { type: 'string', required: true },
      start_date: { type: 'string', required: true, format: 'YYYYMMDD', default: '20241101' },
      end_date: { type: 'string', required: true, format: 'YYYYMMDD', default: '20241201' },
    },
    openapi: {
      description: 'Retrieve token usage records for a bot within a specified date range.',
    },
  })
  async reportUsage(
    ctx: Context<TokenUsageDailyServiceTypes.ActionParams<'reportUsage'>, ApiGatewayMeta>,
  ): Promise<TokenUsageDailyServiceTypes.ActionReturn<'reportUsage'>> {
    const { bot_uuid, start_date, end_date } = ctx.params;

    const parsedStartDate = new Date(`${start_date.slice(0, 4)}-${start_date.slice(4, 6)}-${start_date.slice(6, 8)}`);
    const parsedEndDate = new Date(`${end_date.slice(0, 4)}-${end_date.slice(4, 6)}-${end_date.slice(6, 8)}`);

    // Check date start_date and end_date should not over 60 days
    if (parsedEndDate.getTime() - parsedStartDate.getTime() > 60 * 24 * 60 * 60 * 1000) {
      throw new CommonErrors.UnprocessableError([
        {
          message: 'The range of start_date and end_date should not exceed 60 days.',
          field: 'end_date',
        },
      ]);
    }

    // Assuming teamId is stored in some context meta or can be fetched from a user service
    const teamId = ctx.meta.user.teamId;

    const botSetting = await this.broker.call(
      'botSetting.actionLocalGet',
      { _id: bot_uuid, environment: 'dev' },
      { parentCtx: ctx },
    );

    if (!botSetting || botSetting.team_id !== teamId) {
      throw new CommonErrors.ForbiddenError('Unauthorized: bot_uuid does not match teamId');
    }

    return this.adapter.model
      .find(
        {
          bot_uuid,
          date: { $gte: parsedStartDate, $lte: parsedEndDate },
        },
        this.settings.fields.join(' '),
      )
      .lean({ parseId: true });
  }
}

export = TokenUsageDailyService;
