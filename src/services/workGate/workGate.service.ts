'use strict';
import { getModelForClass } from '@typegoose/typegoose';
import { IBotSetting } from 'entities/botSetting.entity';
import _ from 'lodash';
import { Action, Event, Method, Service as DService } from 'moleculer-decorators';
import * as MoleculerTs from 'moleculer-ts';
import { RequireRoles } from '../../commons/annotation.helper';
import { CommonConfig } from '../../commons/common.config';
import { CommonErrors } from '../../commons/error.helper';
import {
  FacebookFeedEvent,
  IWorkGate,
  MessengerWorkGateConfig,
  WebhookWorkGateConfig,
  WORKGATE_STATUSES,
  WORKGATE_TYPES,
  ZaloQRServerGateConfig,
  ZaloQrWorkGateConfig,
} from '../../entities/workGate.entity';
import { DbBaseMixin, DbBaseMixinActionTypes } from '../../mixins';
import ConfigMixin from '../../mixins/config.mixin';
import { WorkGate, WorkGateSchema, WorkGateType } from '../../models/workGate';
import { ApiGatewayMeta, MoleculerDBService, ObjectIdNull, WorkGateServiceTypes } from '../../types';
import { Context, eventName } from '../../types/moleculer';
import { convertPaginateOptions, convertPaginateQuery } from '../../utils/mongoPlugins/paginate';
import { OwnActions } from './workGate.service.types';

const dbBaseMixin = new DbBaseMixin({
  dbUri: CommonConfig.DB_URI,
  name: 'dbWorkGateMixin',
  collection: 'workGate',
  model: getModelForClass(WorkGate),
});

const allowUpdateFields = _.pick(WorkGateSchema, ['_id', 'description', 'config', 'status']);
Object.keys(allowUpdateFields).forEach((key) => {
  // @ts-ignore
  allowUpdateFields[key].optional = true;
});

// Example for config field
allowUpdateFields['config'].example = {
  on_chat_bot_uuid: '550e8400-e29b-41d4-a716-************',
  new_settings: 'value',
};

// @ts-ignore
allowUpdateFields['$$strict'] = true;

@DService({
  name: 'workGate',
  mixins: [ConfigMixin(['workGate.**']), dbBaseMixin.getMixin(async (adapter) => {}, [])],
  settings: {
    fields: ['_id', 'description', 'type', 'status', 'config', 'gateConfig', 'team_id', 'secret', 'created', 'updated'],
    listFields: ['_id', 'description', 'type', 'status'],
  },
})
class WorkGateService
  extends MoleculerDBService<
    {
      fields: string[];
      listFields: string[];
    },
    WorkGateType
  >
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes>
{
  @RequireRoles()
  @Action({
    rest: 'POST /list',
    cache: {
      keys: ['#user.teamId', 'bot_uuid', 'search', 'sort', 'page', 'pageSize'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true },
      pageSize: { type: 'number', integer: true, min: 0, optional: true, convert: true },
      sort: { type: 'string', optional: true },
      search: { type: 'string', optional: true },
      bot_uuid: { type: 'string', optional: true },
    },
  })
  async actionList(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionList'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionList'>> {
    const query: any = { team_id: ctx.meta.user.teamId };

    if (ctx.params.bot_uuid) {
      query['config.on_chat_bot_uuid'] = ctx.params.bot_uuid;
    }

    return this.adapter.model.paginate(
      convertPaginateQuery({ searchFields: ['description'], ...ctx.params, query }),
      convertPaginateOptions(
        { ...ctx.params, fields: this.settings.listFields },
        this.adapter.model.defaultPaginateOptions,
      ),
    );
  }

  @Action({
    params: {
      $$strict: true,
      query: { type: 'object', optional: true, default: {} },
    },
  })
  async actionCount(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionCount'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionCount'>> {
    const query: any = {
      ...ctx.params.query,
    };

    const count = await this.adapter.model.countDocuments(query);
    return { count };
  }

  @RequireRoles()
  @Action({
    rest: 'POST /create',
    params: {
      description: { type: 'string', min: 3, max: 1000, optional: true, example: 'Zalo 1' },
      type: { type: 'number', enum: Object.values(WORKGATE_TYPES) },
      config: {
        type: 'object',
        optional: true,
        example: { on_chat_bot_uuid: '550e8400-e29b-41d4-a716-************', other_setting: 'value' },
      },
    },
  })
  async actionCreate(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionCreate'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionCreate'>> {
    // Check connector limit before creating
    const teamPackage = await this.broker.call('teamPackage.actionGetCurrent', {}, { parentCtx: ctx });

    if (teamPackage.connectorLimit !== undefined && teamPackage.connectorLimit > 0) {
      // Count current active workgates for the team
      const countResult = await this.broker.call(
        'workGate.actionCount',
        {
          query: {
            team_id: ctx.meta.user.teamId,
            type: ctx.params.type,
            status: { $ne: WORKGATE_STATUSES.DISABLED },
          },
        },
        { parentCtx: ctx },
      );

      const currentWorkGateCount = countResult.count;

      if (currentWorkGateCount >= teamPackage.connectorLimit) {
        throw new CommonErrors.CustomValidationError([
          {
            type: 'LimitExceeded',
            message: `Connector limit exceeded. Your package allows maximum ${teamPackage.connectorLimit} connectors, but you already have ${currentWorkGateCount} active connectors.`,
            field: 'connectorLimit',
            actual: currentWorkGateCount,
          },
        ]);
      }
    }

    const data: IWorkGate = {
      ...ctx.params,
      team_id: ctx.meta.user.teamId,
      status: WORKGATE_STATUSES.ACTIVE,
      config: ctx.params.config,
      // Generate random string secret after creation
      secret: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
    };

    await this.validateWorkGateModel(data, ctx);

    const result = await this.adapter.insert(data);
    const doc = (await this.transformDocuments(ctx, {}, result)) as IWorkGate;

    // Handle post-save actions
    await this.handlePostSaveActions(doc, ctx);

    this.broker.broadcast('workGate.createWorkGate', doc);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'PUT /update',
    params: allowUpdateFields,
  })
  async actionUpdate(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionUpdate'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionUpdate'>> {
    let data = await this.adapter.model.findOne({ _id: ctx.params._id, team_id: ctx.meta.user.teamId });
    if (!data) {
      throw new CommonErrors.NotFoundError('WorkGate not found');
    }

    data = _.merge(data, ctx.params);

    data.config = {
      ...data.config,
      ...ctx.params.config,
    };

    await this.validateWorkGateModel(data, ctx);
    if (ctx.params.config) {
      data.markModified('config');
    }

    await data.save();

    const doc = (await this.transformDocuments(ctx, {}, data)) as IWorkGate;

    // Handle post-save actions
    await this.handlePostSaveActions(doc, ctx);

    this.broker.broadcast('workGate.updateWorkGate', doc);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /get',
    cache: {
      keys: ['#user.teamId', '_id'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
    },
  })
  async actionGet(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionGet'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionGet'>> {
    const data = await this.adapter.findOne({ _id: ctx.params._id, team_id: ctx.meta.user.teamId });
    if (!data) {
      throw new CommonErrors.NotFoundError('WorkGate not found');
    }
    return (await this.transformDocuments(ctx, {}, data)) as IWorkGate;
  }

  @RequireRoles()
  @Action({
    rest: 'DELETE /:id',
    params: {
      $$strict: true,
      id: { type: 'string', optional: false },
    },
  })
  async actionDelete(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionDelete'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionDelete'>> {
    const doc = await this.adapter.model
      .findOne({
        _id: ctx.params.id,
        team_id: ctx.meta.user.teamId,
      })
      .lean({ parseId: true });
    if (!doc) {
      throw new CommonErrors.NotFoundError('WorkGate not found');
    }

    // Handle pre-delete actions
    await this.handlePreDeleteActions(doc, ctx);

    await this.adapter.removeMany({ _id: ctx.params.id });
    this.broker.broadcast('workGate.removeWorkGate', doc);
    return true;
  }

  @Method
  @Event({
    name: eventName('workGate.createWorkGate'),
  })
  cleanCacheOnCreate(workGate: IWorkGate) {
    this.cleanCache(workGate);
  }

  @Method
  @Event({
    name: eventName('workGate.updateWorkGate'),
  })
  cleanCacheOnUpdate(workGate: IWorkGate) {
    this.cleanCache(workGate);
  }

  @Method
  @Event({
    name: eventName('workGate.removeWorkGate'),
  })
  cleanCacheOnRemove(workGate: IWorkGate) {
    this.cleanCache(workGate);
  }

  @Method
  private async validateBotUUID(
    botUUID: string,
    teamId: string | ObjectIdNull,
    ctx: Context,
    fieldName: string,
  ): Promise<void> {
    const bot: IBotSetting | undefined = await this.broker
      .call(
        'botSetting.actionLocalGet',
        {
          _id: botUUID,
          environment: 'dev',
        },
        { parentCtx: ctx },
      )
      .catch((_): undefined => {
        return undefined;
      });

    if (!bot || bot.team_id !== teamId) {
      throw new CommonErrors.CustomValidationError([
        {
          type: 'NotFound',
          message: 'Bot not found or not in your team',
          field: fieldName,
          actual: botUUID,
        },
      ]);
    }
  }

  @Method
  private async validateWorkGateModel(workGate: IWorkGate, ctx: Context): Promise<void> {
    // Validate on_chat_bot_uuid in config if provided
    if (workGate.config?.on_chat_bot_uuid) {
      await this.validateBotUUID(workGate.config.on_chat_bot_uuid, workGate.team_id, ctx, 'config.on_chat_bot_uuid');
    }

    // Check same facebook chat id existed on db
    if (workGate.type === WORKGATE_TYPES.MESSENGER) {
      const messengerConfig = workGate.config as MessengerWorkGateConfig;
      if (messengerConfig.facebook_page_id) {
        const workGateExisted = await this.adapter.findOne({
          'config.facebook_page_id': messengerConfig.facebook_page_id,
        });
        if (workGateExisted && workGateExisted._id != workGate._id) {
          throw new CommonErrors.CustomValidationError([
            {
              type: 'Duplicate',
              message: 'Facebook page id already existed',
              field: 'config.facebook_page_id',
              actual: messengerConfig.facebook_page_id,
            },
          ]);
        }
      }

      // Validate on_feed_events bot UUIDs if provided
      if (messengerConfig.on_feed_events) {
        for (const [postId, eventConfig] of Object.entries(messengerConfig.on_feed_events)) {
          const feedEvent = eventConfig as FacebookFeedEvent;
          if (feedEvent.bot_uuid) {
            await this.validateBotUUID(
              feedEvent.bot_uuid,
              workGate.team_id,
              ctx,
              `config.on_feed_events.${postId}.bot_uuid`,
            );
          }
        }
      }
    }

    // Add more validations here if needed
  }

  @Method
  private cleanCache(workGate: IWorkGate): void {
    const meta = { user: { teamId: workGate.team_id } };
    const cacheKeys = [
      this.broker.cacher.getCacheKey('workGate.actionList', { bot_uuid: '*' }, meta, ['#user.teamId', 'bot_uuid']),
      this.broker.cacher.getCacheKey('workGate.actionGet', { _id: workGate._id }, meta, ['#user.teamId', '_id']),
      this.broker.cacher.getCacheKey('workGate.getConfig', {}, meta, ['#user.teamId']),
      this.broker.cacher.getCacheKey(
        'workGate.actionLocalGet',
        {
          _id: workGate._id,
          query: '*',
        },
        meta,
        ['_id', 'query'],
      ),
    ];
    this.broker.cacher.clean(cacheKeys);
  }

  @Method
  private async handlePostSaveActions(workGate: IWorkGate, ctx: Context): Promise<void> {
    // Handle different workgate types
    switch (workGate.type) {
      case WORKGATE_TYPES.MESSENGER:
        await this.handleMessengerPostSave(workGate, ctx);
        break;
      // Add cases for other workgate types as needed
      // case WORKGATE_TYPES.TELEGRAM:
      //   await this.handleTelegramPostSave(workGate, ctx);
      //   break;
      default:
        // No post-save actions for other types
        break;
    }
  }

  @Method
  private async handlePreDeleteActions(workGate: IWorkGate, ctx: Context): Promise<void> {
    // Handle different workgate types
    switch (workGate.type) {
      case WORKGATE_TYPES.MESSENGER:
        await this.handleMessengerPreDelete(workGate, ctx);
        break;
      // Add cases for other workgate types as needed
      // case WORKGATE_TYPES.TELEGRAM:
      //   await this.handleTelegramPreDelete(workGate, ctx);
      //   break;
      default:
        // No pre-delete actions for other types
        break;
    }
  }

  @Method
  private async handleMessengerPostSave(workGate: IWorkGate, ctx: Context): Promise<void> {
    try {
      const messengerConfig = workGate.config as MessengerWorkGateConfig;
      if (!messengerConfig.facebook_page_id || !messengerConfig.page_access_token) {
        this.logger.warn('Cannot subscribe to feed page events: missing page ID or access token', {
          workGateId: workGate._id,
        });
        return;
      }

      // Subscribe to Facebook page feed events using the Graph API
      const pageId = messengerConfig.facebook_page_id;
      const accessToken = messengerConfig.page_access_token;

      try {
        // Subscribe to page feed events
        const response = await fetch(
          `https://graph.facebook.com/v22.0/${pageId}/subscribed_apps?subscribed_fields=feed,messages&access_token=${accessToken}`,
          { method: 'POST' },
        );

        const result = await response.json();

        if (result.success) {
          this.logger.info('Successfully subscribed to feed page events', {
            workGateId: workGate._id,
            pageId: pageId,
          });
        } else {
          this.logger.warn('Failed to subscribe to feed page events', {
            workGateId: workGate._id,
            pageId: pageId,
            response: result,
          });
        }
      } catch (apiError) {
        this.logger.error('API error when subscribing to feed page events', {
          workGateId: workGate._id,
          pageId: pageId,
          error: apiError.message,
        });
      }
    } catch (error) {
      this.logger.error('Error in Messenger post-save process', {
        workGateId: workGate._id,
        error: error.message,
      });
    }
  }

  @Method
  private async handleMessengerPreDelete(workGate: IWorkGate, ctx: Context): Promise<void> {
    try {
      const messengerConfig = workGate.config as MessengerWorkGateConfig;
      if (!messengerConfig.facebook_page_id || !messengerConfig.page_access_token) {
        this.logger.warn('Cannot unsubscribe from feed page events: missing page ID or access token', {
          workGateId: workGate._id,
        });
        return;
      }

      // Unsubscribe from Facebook page feed events using the Graph API
      const pageId = messengerConfig.facebook_page_id;
      const accessToken = messengerConfig.page_access_token;

      try {
        // Unsubscribe from page feed events by making a DELETE request
        const response = await fetch(
          `https://graph.facebook.com/v22.0/${pageId}/subscribed_apps?subscribed_fields=feed,messages&access_token=${accessToken}`,
          { method: 'DELETE' },
        );

        const result = await response.json();

        if (result.success) {
          this.logger.info('Successfully unsubscribed from feed page events', {
            workGateId: workGate._id,
            pageId: pageId,
          });
        } else {
          this.logger.warn('Failed to unsubscribe from feed page events', {
            workGateId: workGate._id,
            pageId: pageId,
            response: result,
          });
        }
      } catch (apiError) {
        this.logger.error('API error when unsubscribing from feed page events', {
          workGateId: workGate._id,
          pageId: pageId,
          error: apiError.message,
        });
      }
    } catch (error) {
      this.logger.error('Error in Messenger pre-delete process', {
        workGateId: workGate._id,
        error: error.message,
      });
    }
  }

  @Action({
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
      gateConfig: { type: 'object', optional: false },
    },
  })
  async actionUpdateGateConfig(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionUpdateGateConfig'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionUpdateGateConfig'>> {
    let data = await this.adapter.model.findOne({ _id: ctx.params._id });
    if (!data) {
      throw new CommonErrors.NotFoundError('WorkGate not found');
    }

    // Only update if config has changed
    const newGateConfig = {
      ...data.gateConfig,
      ...ctx.params.gateConfig,
    };

    if (JSON.stringify(data.gateConfig) !== JSON.stringify(newGateConfig)) {
      data.gateConfig = newGateConfig;
      data.markModified('gateConfig');
    } else {
      // No changes needed
      console.log('No change actionUpdateGateConfig', ctx.params._id);
      return (await this.transformDocuments(ctx, {}, data)) as IWorkGate;
    }

    await data.save();

    const doc = (await this.transformDocuments(ctx, {}, data)) as IWorkGate;
    this.broker.broadcast('workGate.updateWorkGate', doc);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'GET /getConfig',
    cache: {
      keys: ['#user.teamId'],
      ttl: 60 * 30, // 30 minutes
    },
  })
  async getConfig(
    ctx: Context<WorkGateServiceTypes.ActionParams<'getConfig'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'getConfig'>> {
    return {
      workGateTypes: [
        { key: 'Messenger', value: WORKGATE_TYPES.MESSENGER },
        { key: 'Telegram', value: WORKGATE_TYPES.TELEGRAM },
        { key: 'WhatsApp', value: WORKGATE_TYPES.WHATSAPP },
        { key: 'Zalo Personal', value: WORKGATE_TYPES.ZALO_PERSONAL },
        { key: 'Webhook', value: WORKGATE_TYPES.WEBHOOK },
      ],
      workGateStatuses: [
        { key: 'Active', value: WORKGATE_STATUSES.ACTIVE },
        { key: 'Inactive', value: WORKGATE_STATUSES.INACTIVE },
        { key: 'Disabled', value: WORKGATE_STATUSES.DISABLED },
      ],
    };
  }

  @Action({
    cache: {
      keys: ['_id', 'query'],
      ttl: 60 * 5, // 5 minutes
      enabled: (ctx) => !!ctx.params._id, // Only cache if has _id
    },
    params: {
      $$strict: true,
      _id: { type: 'string', optional: true },
      query: { type: 'object', optional: true, default: {}, example: { type: WORKGATE_TYPES.ZALO_PERSONAL } },
    },
  })
  async actionLocalGet(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionLocalGet'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionLocalGet'>> {
    if (!ctx.params._id && _.isEmpty(ctx.params.query)) {
      throw new CommonErrors.NotFoundError('Condition invalid!');
    }
    const query: any = {
      status: { $ne: WORKGATE_STATUSES.DISABLED },
      ...ctx.params.query,
    };

    if (ctx.params._id) {
      query._id = ctx.params._id;
    }

    const data = await this.adapter.findOne(query);
    if (!data) {
      throw new CommonErrors.NotFoundError('WorkGate not found or is disabled');
    }
    return (await this.transformDocuments(ctx, {}, data)) as IWorkGate;
  }

  @RequireRoles()
  @Action({
    rest: 'GET /getListFacebookPosts',
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
    },
  })
  async getListFacebookPosts(
    ctx: Context<WorkGateServiceTypes.ActionParams<'getListFacebookPosts'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'getListFacebookPosts'>> {
    // Get the workgate
    const workGate = await this.adapter.findOne({ _id: ctx.params._id, team_id: ctx.meta.user.teamId });
    if (!workGate) {
      throw new CommonErrors.NotFoundError('WorkGate not found');
    }

    // Check if it's a Facebook workgate
    if (workGate.type !== WORKGATE_TYPES.MESSENGER) {
      throw new CommonErrors.CustomValidationError([
        {
          type: 'InvalidType',
          message: 'WorkGate is not a Facebook Messenger type',
          field: 'type',
          actual: workGate.type,
        },
      ]);
    }

    const messengerConfig = workGate.config as MessengerWorkGateConfig;
    if (!messengerConfig.facebook_page_id || !messengerConfig.page_access_token) {
      throw new CommonErrors.CustomValidationError([
        {
          type: 'MissingConfig',
          message: 'Facebook page ID or access token is missing',
          field: 'config',
          actual: messengerConfig,
        },
      ]);
    }

    // Call Facebook Graph API to get posts
    try {
      const response = await fetch(
        `https://graph.facebook.com/v22.0/${messengerConfig.facebook_page_id}/posts?access_token=${messengerConfig.page_access_token}&fields=id,message,created_time&limit=20`,
      );

      if (!response.ok) {
        throw new CommonErrors.CustomValidationError([
          {
            type: 'FacebookAPIError',
            message: 'Failed to fetch Facebook posts',
            field: 'config.page_access_token',
            actual: response.statusText,
          },
        ]);
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      throw new CommonErrors.CustomValidationError([
        {
          type: 'FacebookAPIError',
          message: 'Failed to fetch Facebook posts',
          field: 'config.page_access_token',
          actual: error.message,
        },
      ]);
    }
  }

  @RequireRoles()
  @Action({
    rest: 'POST /importFacebookPages',
    params: {
      $$strict: true,
      configs: {
        type: 'array',
        items: {
          type: 'object',
          props: {
            facebook_page_id: { type: 'string' },
            page_access_token: { type: 'string' },
            facebook_page_name: { type: 'string' },
          },
        },
      },
    },
  })
  async actionImportFacebookPages(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionImportFacebookPages'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionImportFacebookPages'>> {
    const results = {
      created: [] as IWorkGate[],
      updated: [] as IWorkGate[],
      errors: [] as { config: any; error: string }[],
    };

    for (const config of ctx.params.configs) {
      try {
        // Check if workgate with this facebook_page_id already exists
        const existingWorkGate = await this.adapter.findOne({
          type: WORKGATE_TYPES.MESSENGER,
          'config.facebook_page_id': config.facebook_page_id,
        });

        if (existingWorkGate) {
          // Update existing workgate
          const existingConfig = existingWorkGate.config as MessengerWorkGateConfig;
          const updatedConfig: MessengerWorkGateConfig = {
            ...config,
            on_feed_events: existingConfig.on_feed_events,
            on_chat_bot_uuid: existingConfig.on_chat_bot_uuid,
          };

          existingWorkGate.config = updatedConfig;
          existingWorkGate.markModified('config');

          await this.validateWorkGateModel(existingWorkGate, ctx);
          await existingWorkGate.save();

          const doc = (await this.transformDocuments(ctx, {}, existingWorkGate)) as IWorkGate;

          // Handle post-save actions
          await this.handlePostSaveActions(doc, ctx);

          this.broker.broadcast('workGate.updateWorkGate', doc);
          results.updated.push(doc);
        } else {
          // Create new workgate
          const newWorkGate: IWorkGate = {
            type: WORKGATE_TYPES.MESSENGER,
            status: WORKGATE_STATUSES.ACTIVE,
            team_id: ctx.meta.user.teamId,
            description: `Fb page-${config.facebook_page_name}`,
            config: {
              ...config,
              on_feed_events: {},
            },
            secret: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
          };

          await this.validateWorkGateModel(newWorkGate, ctx);
          const result = await this.adapter.insert(newWorkGate);
          const doc = (await this.transformDocuments(ctx, {}, result)) as IWorkGate;

          // Handle post-save actions
          await this.handlePostSaveActions(doc, ctx);

          this.broker.broadcast('workGate.createWorkGate', doc);
          results.created.push(doc);
        }
      } catch (error) {
        console.log('error', error);
        results.errors.push({
          config,
          error: error.message,
        });
      }
    }

    return results;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /webhook/:bot_uuid',
    openapi: {
      summary: 'Generate webhook',
      description:
        'Use this to create or update webhook settings. Change mode to prod to generate production webhook. Ips is filter whitelist but nhác. Do later.',
    },
    params: {
      $$strict: true,
      bot_uuid: { type: 'string', required: true },
      slug_url: {
        type: 'string',
        required: true,
        pattern: '^[a-z0-9-_]{8,}$',
        description: 'Unique slug URL (min 8 chars, lowercase letters, numbers, hyphens only)',
      },
      mode: {
        type: 'string',
        enum: ['dev', 'prod'],
        default: 'dev',
        optional: true,
      },
      ips: {
        type: 'string',
        optional: true,
      },
    },
  })
  async actionPostWebhook(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionPostWebhook'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionPostWebhook'>> {
    const { bot_uuid, slug_url, mode, ips } = ctx.params;

    // Validate bot exists and belongs to team
    await this.validateBotUUID(bot_uuid, ctx.meta.user.teamId, ctx, 'bot_uuid');

    // Check if slug_url is unique
    const existingWebhook = await this.adapter.findOne({
      type: WORKGATE_TYPES.WEBHOOK,
      'config.on_chat_bot_uuid': bot_uuid,
    });

    if (existingWebhook) {
      // Update existing webhook
      const existingConfig = existingWebhook.config as WebhookWorkGateConfig;
      const updatedConfig: WebhookWorkGateConfig = {
        ...existingConfig,
        slug_url: slug_url,
        mode: mode,
        ips: ips,
      };

      existingWebhook.config = updatedConfig;
      existingWebhook.markModified('config');

      await this.validateWorkGateModel(existingWebhook, ctx);
      await existingWebhook.save();

      const doc = (await this.transformDocuments(ctx, {}, existingWebhook)) as IWorkGate;
      this.broker.broadcast('workGate.updateWorkGate', doc);

      const config = doc.config as WebhookWorkGateConfig;
      return {
        id: doc._id,
        bot_uuid: config.on_chat_bot_uuid,
        mode: config.mode,
        ips: config.ips,
        webhook_url: `${CommonConfig.REST_BASE_URL}/api/botChat/hook/${config.slug_url}`,
      };
    } else {
      // Create new webhook
      const newWorkGate: IWorkGate = {
        type: WORKGATE_TYPES.WEBHOOK,
        status: WORKGATE_STATUSES.ACTIVE,
        team_id: ctx.meta.user.teamId,
        description: `Webhook-${slug_url}`,
        config: {
          slug_url,
          mode: mode || 'dev',
          ips: ips || '',
          on_chat_bot_uuid: bot_uuid,
        },
        secret: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
      };

      await this.validateWorkGateModel(newWorkGate, ctx);
      const result = await this.adapter.insert(newWorkGate);
      const doc = (await this.transformDocuments(ctx, {}, result)) as IWorkGate;
      this.broker.broadcast('workGate.createWorkGate', doc);

      const config = doc.config as WebhookWorkGateConfig;
      return {
        id: doc._id,
        bot_uuid: config.on_chat_bot_uuid,
        mode: config.mode,
        ips: config.ips,
        webhook_url: `${CommonConfig.REST_BASE_URL}/api/botChat/hook/${config.slug_url}`,
      };
    }
  }

  @RequireRoles()
  @Action({
    rest: 'GET /webhook/:bot_uuid',
    openapi: {
      summary: 'Get webhook info',
      description: 'Get webhook info for a bot. Use this to view and update webhook settings.',
    },
    params: {
      $$strict: true,
      bot_uuid: { type: 'string', required: true },
    },
  })
  async actionGetWebhook(
    ctx: Context<WorkGateServiceTypes.ActionParams<'actionGetWebhook'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'actionGetWebhook'>> {
    const webhook = await this.adapter.findOne({
      type: WORKGATE_TYPES.WEBHOOK,
      'config.on_chat_bot_uuid': ctx.params.bot_uuid,
      team_id: ctx.meta.user.teamId,
    });

    if (!webhook) {
      throw new CommonErrors.NotFoundError('Webhook not found or inactive');
    }

    const config = webhook.config as WebhookWorkGateConfig;
    return {
      id: webhook._id,
      bot_uuid: config.on_chat_bot_uuid,
      mode: config.mode,
      ips: config.ips,
      webhook_url: `${CommonConfig.REST_BASE_URL}/api/botChat/hook/${config.slug_url}`,
    };
  }

  @Action({
    params: {
      $$strict: true,
    },
  })
  async getAllZaloQr(
    ctx: Context<WorkGateServiceTypes.ActionParams<'getAllZaloQr'>, ApiGatewayMeta>,
  ): Promise<WorkGateServiceTypes.ActionReturn<'getAllZaloQr'>> {
    const workGates = await this.adapter.model
      .find({
        type: WORKGATE_TYPES.ZALO_QR,
        status: { $ne: WORKGATE_STATUSES.DISABLED },
      })
      .lean({ parseId: true });

    return workGates.map((workGate) => ({
      id: workGate._id,
      team_id: workGate.team_id,
      status: workGate.status,
      config: workGate.config as ZaloQrWorkGateConfig,
      gateConfig: workGate.gateConfig as ZaloQRServerGateConfig,
    }));
  }

  created() {
    // this.waitForServices(['api']);
  }
}

export = WorkGateService;
