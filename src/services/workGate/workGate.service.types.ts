import { Action, ConcatMultiple, Event } from 'moleculer-ts';
import { IWorkGate, WORKGATE_TYPE, ZaloQRServerGateConfig, ZaloQrWorkGateConfig } from '../../entities/workGate.entity';
import { DbBaseMixinActions } from '../../mixins';
import { ActionListQuery, DBPagination } from '../../types';
import { FilterQuery } from 'mongoose';

export const name: 'workGate' = 'workGate';

export type WorkGateConfig = {
  workGateTypes: Array<{
    key: string;
    value: number;
  }>;
  workGateStatuses: Array<{
    key: string;
    value: number;
  }>;
};

export type OwnInternalActions = [
  Action<
    'actionList',
    ActionListQuery & {
    bot_uuid?: string;
  },
    DBPagination<IWorkGate>
  >,
  Action<
    'actionCount',
    {
      query?: FilterQuery<IWorkGate>;
    },
    { count: number }
  >,
  Action<'actionCreate', Omit<IWorkGate, 'team_id' | 'status'>, IWorkGate>,
  Action<'actionUpdateGateConfig', { _id: string; gateConfig: Record<string, any> }, IWorkGate>,
  Action<'actionLocalGet', { _id?: string; query?: Record<string, any> }, IWorkGate>,
  Action<'actionUpdate', IWorkGate, IWorkGate>,
  Action<'actionGet', { _id: string }, IWorkGate>,
  Action<'actionDelete', { id: string }, boolean>,
  Action<'getConfig', {}, WorkGateConfig>,
  Action<'getListFacebookPosts', { _id: string }, Array<{ id: string; message: string; created_time: string }>>,
  Action<
    'actionImportFacebookPages',
    {
      configs: Array<{
        facebook_page_id: string;
        page_access_token: string;
        facebook_page_name: string;
      }>;
    },
    {
      created: IWorkGate[];
      updated: IWorkGate[];
      errors: Array<{ config: any; error: string }>;
    }
  >,
  Action<
    'actionPostWebhook',
    {
      bot_uuid: string;
      slug_url: string;
      mode?: 'dev' | 'prod';
      ips?: string;
    },
    {
      id: string;
      bot_uuid: string;
      mode: 'dev' | 'prod';
      ips: string;
      webhook_url: string;
    }
  >,
  Action<
    'actionGetWebhook',
    {
      bot_uuid: string;
    },
    {
      id: string;
      bot_uuid: string;
      mode: 'dev' | 'prod';
      ips: string;
      webhook_url: string;
    }
  >,
  Action<
    'getAllZaloQr',
    {},
    Array<{
      id: string;
      team_id: string;
      status: WORKGATE_TYPE;
      config: ZaloQrWorkGateConfig;
      gateConfig: ZaloQRServerGateConfig;
    }>
  >,
];

export type OwnActions = ConcatMultiple<[OwnInternalActions, DbBaseMixinActions<IWorkGate>]>;

export type OwnEvents = [
  Event<'createWorkGate', IWorkGate>,
  Event<'updateWorkGate', IWorkGate>,
  Event<'removeWorkGate', IWorkGate>,
];

export type Actions = ConcatMultiple<[OwnActions]>;
export type Events = ConcatMultiple<[OwnEvents]>;
