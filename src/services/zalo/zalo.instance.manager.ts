import { API, Zalo } from 'zca-js';
import { LoggerInstance } from 'moleculer';
import { ZaloQRServerGateConfig } from 'entities/workGate.entity';
import { WorkGateServiceTypes } from '../../types';

// Interfaces for Zalo service
export interface PhoneUserIdCache {
  [phoneNumber: string]: string; // Maps phone numbers to user IDs
}

export interface GroupCache {
  [groupId: string]: {
    name: string;
    lastUpdated: number;
    info: any;
  };
}

export type ZaloAccount = ZaloQRServerGateConfig & {
  // Additional fields specific to the service
  zaloApi: API | null;
  isAuthenticated: boolean;
  phoneToUserIdCache: PhoneUserIdCache;
  groupCache: GroupCache;
  workgate?: WorkGateServiceTypes.ActionReturn<'getAllZaloQr'>[number];
};

export class ZaloInstanceManager {
  private globalZaloInstance: Zalo | null = null;
  private accounts: Map<string, ZaloAccount> = new Map();
  private logger: LoggerInstance;

  constructor(logger: LoggerInstance) {
    this.logger = logger;
  }

  /**
   * Get or create the global Zalo instance
   */
  getZaloInstance(): Zalo {
    if (!this.globalZaloInstance) {
      this.globalZaloInstance = new Zalo({
        logging: false,
        checkUpdate: false,
      });
      this.logger.info('Created global Zalo instance');
    }
    return this.globalZaloInstance;
  }

  /**
   * Get all accounts
   */
  getAllAccounts(): Map<string, ZaloAccount> {
    return this.accounts;
  }

  /**
   * Get account by ID
   */
  getAccount(accountId: string): ZaloAccount | undefined {
    return this.accounts.get(accountId);
  }

  /**
   * Add account to the accounts map
   */
  addAccount(account: ZaloAccount): void {
    this.accounts.set(account.id, account);
    this.logger.info(`Account ${account.id} (${account.displayName}) added to accounts map`);
  }

  /**
   * Remove account from the accounts map
   */
  removeAccount(accountId: string): boolean {
    const account = this.accounts.get(accountId);
    if (account) {
      const success = this.accounts.delete(accountId);
      if (success) {
        this.logger.info(`Removed account ${accountId} (${account.displayName}) from accounts map`);
      }
      return success;
    }
    return false;
  }

  /**
   * Generate a unique account ID from real user data
   */
  generateAccountId(realUserId: string, realUserName: string): string {
    return realUserId;
  }

  /**
   * Validate workgate account data
   */
  isValidWorkgateAccount(workgateAccount: any): boolean {
    if (!workgateAccount || !workgateAccount.gateConfig) {
      return false;
    }

    const { gateConfig } = workgateAccount;

    // Check required fields
    if (!gateConfig.id || !gateConfig.displayName || !gateConfig.sessionData) {
      return false;
    }

    // Check session data
    const { sessionData } = gateConfig;
    if (!sessionData.cookie || !sessionData.imei || !sessionData.userAgent) {
      return false;
    }

    return true;
  }

  /**
   * Create a new account instance
   */
  createAccount(workgateAccount: WorkGateServiceTypes.ActionReturn<'getAllZaloQr'>[number]): ZaloAccount {
    return {
      id: workgateAccount.gateConfig.id,
      displayName: workgateAccount.gateConfig.displayName,
      phoneNumber: workgateAccount.gateConfig.phoneNumber || '',
      user: workgateAccount.gateConfig.user || null,
      sessionData: {
        imei: workgateAccount.gateConfig.sessionData.imei,
        userAgent: workgateAccount.gateConfig.sessionData.userAgent,
        cookie: workgateAccount.gateConfig.sessionData.cookie,
      },
      savedAt: workgateAccount.gateConfig.savedAt,
      zaloApi: null,
      isAuthenticated: false,
      phoneToUserIdCache: {},
      groupCache: {},
      workgate: {
        id: workgateAccount.id,
        team_id: workgateAccount.team_id,
        status: workgateAccount.status,
        config: workgateAccount.config,
        gateConfig: workgateAccount.gateConfig,
      },
    };
  }

  /**
   * Create a temporary account for login
   */
  createTempAccount(): ZaloAccount {
    return {
      id: '', // Will be set after successful login with real user ID
      displayName: 'Logging in...',
      phoneNumber: '',
      user: null,
      sessionData: {
        imei: '',
        userAgent: '',
        cookie: null,
      },
      savedAt: Date.now(),
      zaloApi: null,
      isAuthenticated: false,
      phoneToUserIdCache: {},
      groupCache: {},
      workgate: undefined,
    };
  }

  /**
   * Clean up account state and optionally remove from accounts map
   */
  cleanupAccount(account: ZaloAccount, removeFromMap: boolean = false, reason?: string): void {
    const accountId = account.id;
    const accountName = account.displayName;

    try {
      // Stop listener if exists
      if (account.zaloApi && account.zaloApi.listener) {
        account.zaloApi.listener.stop();
        this.logger.info(`Stopped listener for account ${accountId}`);
      }

      // Reset account state
      account.zaloApi = null;
      account.isAuthenticated = false;
      account.user = null;
      account.phoneToUserIdCache = {};
      account.groupCache = {};
      account.workgate = undefined;

      // Remove from accounts map if requested
      if (removeFromMap) {
        this.accounts.delete(accountId);
        this.logger.info(
          `Removed account ${accountId} (${accountName}) from accounts map${reason ? ` due to ${reason}` : ''}`,
        );
      }

      this.logger.info(`Account ${accountId} (${accountName}) cleaned up successfully`);
    } catch (error) {
      this.logger.error(`Error cleaning up account ${accountId}:`, error as Error);
    }
  }

  /**
   * Get accounts count
   */
  getAccountsCount(): number {
    return this.accounts.size;
  }

  /**
   * Get authenticated accounts count
   */
  getAuthenticatedAccountsCount(): number {
    return Array.from(this.accounts.values()).filter((acc) => acc.isAuthenticated).length;
  }

  /**
   * Get first authenticated account
   */
  getFirstAuthenticatedAccount(): ZaloAccount | null {
    const authenticatedAccounts = Array.from(this.accounts.values()).filter((acc) => acc.isAuthenticated);
    return authenticatedAccounts.length > 0 ? authenticatedAccounts[0] : null;
  }

  /**
   * Get account list for status display
   */
  getAccountList(): Array<{
    id: string;
    name: string;
    user: any;
    isAuthenticated: boolean;
  }> {
    return Array.from(this.accounts.values()).map((acc) => ({
      id: acc.id,
      name: acc.displayName,
      user: acc.user,
      isAuthenticated: acc.isAuthenticated,
    }));
  }
}
