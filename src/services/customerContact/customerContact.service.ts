'use strict';
import { getModelForClass } from '@typegoose/typegoose';
import _ from 'lodash';
import { Action, Event, Method, Service as DService } from 'moleculer-decorators';
import * as MoleculerTs from 'moleculer-ts';
import { RequireRoles } from '../../commons/annotation.helper';
import { CommonConfig } from '../../commons/common.config';
import { CommonErrors } from '../../commons/error.helper';
import { CUSTOMER_CONTACT_STATUS, ICustomerContact } from '../../entities/customerContact.entity';
import { DbBaseMixin, DbBaseMixinActionTypes } from '../../mixins';
import ConfigMixin from '../../mixins/config.mixin';
import { CustomerContact, CustomerContactSchema, CustomerContactType } from '../../models/customerContact';
import { ApiGatewayMeta, CustomerContactServiceTypes, MoleculerDBService } from '../../types';
import { Context, eventName } from '../../types/moleculer';
import { convertPaginateOptions, convertPaginateQuery } from '../../utils/mongoPlugins/paginate';
import { OwnActions } from './customerContact.service.types';

const dbBaseMixin = new DbBaseMixin({
  dbUri: CommonConfig.DB_URI,
  name: 'dbCustomerContactMixin',
  collection: 'customerContact',
  model: getModelForClass(CustomerContact),
});

const allowUpdateFields = _.pick(CustomerContactSchema, ['name', 'phone', 'address', 'note']);

// @ts-ignore
allowUpdateFields['$$strict'] = true;

@DService({
  name: CustomerContactServiceTypes.name,
  mixins: [ConfigMixin(['customerContact.**']), dbBaseMixin.getMixin(async (adapter) => {}, [])],
  settings: {
    itemsPerPage: 20,
    fields: ['_id', 'bot_uuid', 'name', 'phone', 'address', 'note', 'threads', 'status', 'created', 'updated'],
  },
})
class CustomerContactService
  extends MoleculerDBService<
    {
      itemsPerPage: number;
      fields: string[];
    },
    CustomerContactType
  >
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes>
{
  @RequireRoles()
  @Action({
    rest: 'POST /list',
    cache: {
      keys: ['#user.teamId', 'bot_uuid', 'search', 'sort', 'page', 'pageSize'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true },
      pageSize: { type: 'number', integer: true, min: 0, optional: true, convert: true },
      sort: { type: 'string', optional: true },
      search: { type: 'string', optional: true },
      bot_uuid: { type: 'string', optional: true },
    },
  })
  async actionList(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionList'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionList'>> {
    const query: any = { status: { $ne: CUSTOMER_CONTACT_STATUS.DELETED } };

    // Add bot_uuid filter if provided
    if (ctx.params.bot_uuid) {
      query.bot_uuid = ctx.params.bot_uuid;
    }

    return this.adapter.model.paginate(
      {
        ...query,
        ...convertPaginateQuery({ searchFields: ['name', 'phone', 'address'], ...ctx.params }),
      },
      convertPaginateOptions(
        { ...ctx.params, fields: this.settings.fields },
        this.adapter.model.defaultPaginateOptions,
      ),
    );
  }

  @RequireRoles()
  @Action({
    rest: 'POST /create',
    params: {
      $$strict: true,
      bot_uuid: { type: 'string', optional: false },
      name: { type: 'string', optional: false },
      phone: { type: 'string', optional: true },
      address: { type: 'string', optional: true },
      note: { type: 'string', optional: true },
      threads: { type: 'array', items: 'object', optional: true },
    },
  })
  async actionCreate(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionCreate'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionCreate'>> {
    const { bot_uuid, name, phone, address, note, threads } = ctx.params;

    // Create new customer contact
    const customerContact = await this.adapter.insert({
      bot_uuid,
      name,
      phone: phone || '',
      address: address || '',
      note: note || '',
      threads: threads || [],
      status: CUSTOMER_CONTACT_STATUS.ACTIVE,
    });

    const doc = await this.transformDocuments(ctx, {}, customerContact);
    const result = Array.isArray(doc) ? doc[0] : doc;
    this.broker.broadcast('customerContact.created', result);

    return result;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /update',
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
      ...allowUpdateFields,
    },
  })
  async actionUpdate(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionUpdate'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionUpdate'>> {
    const { _id, ...updateData } = ctx.params;

    // Find the customer contact
    const customerContact = await this.adapter.findById(_id);
    if (!customerContact) {
      throw new CommonErrors.NotFoundError('Customer contact not found');
    }

    // Update the customer contact
    const updated = await this.adapter.updateById(_id, { $set: updateData });

    const doc = await this.transformDocuments(ctx, {}, updated);
    const result = Array.isArray(doc) ? doc[0] : doc;
    this.broker.broadcast('customerContact.updated', result);

    return result;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /get',
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
    },
  })
  async actionGet(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionGet'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionGet'>> {
    const { _id } = ctx.params;

    // Find the customer contact
    const customerContact = await this.adapter.findById(_id).catch((): any => undefined);
    if (!customerContact) {
      throw new CommonErrors.NotFoundError('Customer contact not found');
    }

    const doc = await this.transformDocuments(ctx, {}, customerContact);
    return Array.isArray(doc) ? doc[0] : doc;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /delete',
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
    },
  })
  async actionDelete(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionDelete'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionDelete'>> {
    const { _id } = ctx.params;

    // Find the customer contact
    const customerContact = await this.adapter.findById(_id);
    if (!customerContact) {
      throw new CommonErrors.NotFoundError('Customer contact not found');
    }

    // Soft delete by setting status to DELETED
    await this.adapter.updateById(_id, { $set: { status: CUSTOMER_CONTACT_STATUS.DELETED } });

    const doc = (await this.transformDocuments(ctx, {}, customerContact)) as ICustomerContact;
    this.broker.broadcast('customerContact.removed', doc);

    return { success: true };
  }

  @RequireRoles()
  @Action({
    rest: 'POST /quickSearch',
    params: {
      $$strict: true,
      bot_uuid: { type: 'string', optional: false },
      search: { type: 'string', optional: false },
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true, default: 1 },
      pageSize: { type: 'number', integer: true, min: 0, optional: true, convert: true, default: 10 },
    },
  })
  async actionQuickSearch(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionQuickSearch'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionQuickSearch'>> {
    const { bot_uuid, search, page, pageSize } = ctx.params;

    // Create case-insensitive search regex
    const searchRegex = new RegExp(search, 'i');

    // Build query for searching across multiple fields
    const query = {
      bot_uuid,
      status: { $ne: CUSTOMER_CONTACT_STATUS.DELETED },
      $or: [{ name: searchRegex }, { phone: searchRegex }, { address: searchRegex }],
    };

    return this.adapter.model.paginate(
      query,
      convertPaginateOptions(
        { page, pageSize, fields: ['_id', 'name', 'phone', 'address'] },
        this.adapter.model.defaultPaginateOptions,
      ),
    );
  }

  @RequireRoles()
  @Action({
    rest: 'POST /linkChatHistory',
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
      thread_id: { type: 'string', optional: false },
      bot_uuid: { type: 'string', optional: false },
    },
  })
  async actionLinkChatHistory(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionLinkChatHistory'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionLinkChatHistory'>> {
    const { _id, thread_id, bot_uuid } = ctx.params;

    // Verify the customer contact exists
    const customerContact = await this.adapter.findById(_id);
    if (!customerContact) {
      throw new CommonErrors.NotFoundError('Customer contact not found');
    }

    // Verify the bot_uuid matches
    if (customerContact.bot_uuid !== bot_uuid) {
      throw new CommonErrors.ValidationError('Bot UUID mismatch');
    }

    // Get chat history to extract thread info
    const chatHistory = await this.broker.call('chatHistory.find', {
      query: {
        thread_id,
        bot_uuid,
        isFinished: false,
      },
      limit: 1,
      populate: [],
    });

    const chatHistoryItem = Array.isArray(chatHistory) && chatHistory.length > 0 ? chatHistory[0] : null;

    if (!chatHistoryItem) {
      throw new CommonErrors.NotFoundError('Chat history not found');
    }

    // Extract thread type and name from chat history
    const { threadType, threadName } = this.extractThreadInfo(chatHistoryItem, thread_id);

    // Check if thread_id already exists
    const threadExists = customerContact.threads.some((thread) => thread.thread_id === thread_id);

    if (!threadExists) {
      // Add new thread info
      await this.adapter.updateById(_id, {
        $push: {
          threads: {
            thread_id,
            type: threadType,
            name: threadName,
          },
        },
      });
    }

    return { success: true };
  }

  @RequireRoles()
  @Action({
    rest: 'POST /unlinkChatHistory',
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
      thread_id: { type: 'string', optional: false },
    },
  })
  async actionUnlinkChatHistory(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionUnlinkChatHistory'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionUnlinkChatHistory'>> {
    const { _id, thread_id } = ctx.params;

    // Verify the customer contact exists
    const customerContact = await this.adapter.findById(_id);
    if (!customerContact) {
      throw new CommonErrors.NotFoundError('Customer contact not found');
    }

    // Remove thread_id from the list
    await this.adapter.updateById(_id, {
      $pull: { threads: { thread_id: thread_id } },
    });

    return { success: true };
  }

  @RequireRoles()
  @Action({
    rest: 'POST /getByThreadId',
    params: {
      $$strict: true,
      thread_id: { type: 'string', optional: false },
      bot_uuid: { type: 'string', optional: false },
    },
  })
  async actionGetByThreadId(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionGetByThreadId'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionGetByThreadId'>> {
    const { thread_id, bot_uuid } = ctx.params;

    // Find customer contact that has this thread_id
    const customerContact = await this.adapter.findOne({
      'threads.thread_id': thread_id,
      bot_uuid,
      status: { $ne: CUSTOMER_CONTACT_STATUS.DELETED },
    });

    if (!customerContact) {
      throw new CommonErrors.NotFoundError('No customer contact found for this thread ID');
    }

    const doc = await this.transformDocuments(ctx, {}, customerContact);
    return Array.isArray(doc) ? doc[0] : doc;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /quickCreateFromChatHistory',
    params: {
      $$strict: true,
      bot_uuid: { type: 'string', optional: false },
      thread_id: { type: 'string', optional: false },
    },
  })
  async actionQuickCreateFromChatHistory(
    ctx: Context<CustomerContactServiceTypes.ActionParams<'actionQuickCreateFromChatHistory'>, ApiGatewayMeta>,
  ): Promise<CustomerContactServiceTypes.ActionReturn<'actionQuickCreateFromChatHistory'>> {
    const { bot_uuid, thread_id } = ctx.params;

    // Check if a customer contact already exists for this thread_id
    const existingContact = await this.adapter.findOne({
      'threads.thread_id': thread_id,
      bot_uuid,
      status: { $ne: CUSTOMER_CONTACT_STATUS.DELETED },
    });

    if (existingContact) {
      const doc = await this.transformDocuments(ctx, {}, existingContact);
      return Array.isArray(doc) ? doc[0] : doc;
    }

    // Get chat history to extract profile information
    const chatHistoryData = await this.broker.call('chatHistory.find', {
      query: {
        bot_uuid,
        thread_id,
        isFinished: false,
      },
      limit: 1,
      populate: [],
    });

    const chatHistoryItem = Array.isArray(chatHistoryData) && chatHistoryData.length > 0 ? chatHistoryData[0] : null;

    if (!chatHistoryItem) {
      throw new CommonErrors.NotFoundError('Chat history not found');
    }

    // Extract profile from session
    // Cast to IChatHistory to access session property
    const chatHistory = chatHistoryItem as unknown as { session?: { profile?: any } };
    const profile = chatHistory.session?.profile || {};

    // Extract thread type and name from chat history
    const { threadType, threadName } = this.extractThreadInfo(chatHistoryItem, thread_id);

    // Create new customer contact with profile data
    const customerContact = await this.adapter.insert({
      bot_uuid,
      name: profile.name || threadName || 'Unknown',
      phone: profile.phone || '',
      address: profile.address || '',
      note: '',
      threads: [
        {
          thread_id,
          type: threadType,
          name: threadName,
        },
      ],
      status: CUSTOMER_CONTACT_STATUS.ACTIVE,
    });

    const doc = await this.transformDocuments(ctx, {}, customerContact);
    const result = Array.isArray(doc) ? doc[0] : doc;
    this.broker.broadcast('customerContact.created', result);

    return result;
  }

  /**
   * Extract thread type and name from chat history item
   * @param chatHistoryItem The chat history item
   * @param thread_id The thread ID (used as fallback for thread name)
   * @returns Object containing threadType and threadName
   */
  @Method
  private extractThreadInfo(chatHistoryItem: any, thread_id: string): { threadType: string; threadName: string } {
    // Extract profile from session
    const chatHistoryWithSession = chatHistoryItem as unknown as { session?: { profile?: any } };
    const profile = chatHistoryWithSession.session?.profile || {};

    // Get channel_id from chat history
    const chatHistoryWithChannel = chatHistoryItem as unknown as { channel_id?: string };
    const channel_id = chatHistoryWithChannel.channel_id || '';

    // Special case: if channel_id is 'web', use it as the type
    if (channel_id === 'web') {
      return {
        threadType: 'web',
        threadName: profile.name || thread_id,
      };
    }

    // Extract thread type from channel_id (e.g., "fb:468853089634777" -> "fb")
    const channelParts = channel_id.split(':');
    const threadType = channelParts.length > 0 ? channelParts[0] : 'other';

    // Determine thread name based on type and profile
    let threadName = '';
    if (profile.name) {
      threadName = profile.name;
    } else {
      // If no profile name, use appropriate identifier based on type
      if (threadType === 'zalo' && profile.phone) {
        threadName = profile.phone;
      } else if (threadType === 'fb' && profile.email) {
        threadName = profile.email;
      } else {
        // Use channel_id suffix as a fallback
        threadName = channelParts.length > 1 ? channelParts[1] : channel_id;
      }
    }

    // Default value if still not set
    if (!threadName) {
      threadName = thread_id;
    }

    return { threadType, threadName };
  }

  @Method
  @Event({
    name: eventName('customerContact.created'),
  })
  cleanCacheOnCreate(customerContact: ICustomerContact) {
    this.cleanCache(customerContact);
  }

  @Method
  @Event({
    name: eventName('customerContact.updated'),
  })
  cleanCacheOnUpdate(customerContact: ICustomerContact) {
    this.cleanCache(customerContact);
  }

  @Method
  @Event({
    name: eventName('customerContact.removed'),
  })
  cleanCacheOnRemove(customerContact: ICustomerContact) {
    this.cleanCache(customerContact);
  }

  @Method
  private cleanCache(customerContact: ICustomerContact) {
    this.broker.logger.info('CustomerContact cache cleaned', customerContact._id);
    const meta = { user: { teamId: '*' } };

    const cacheKeys = [
      // List cache
      this.broker.cacher.getCacheKey(
        'customerContact.actionList',
        {
          bot_uuid: customerContact.bot_uuid,
          search: '*',
        },
        meta,
        ['#user.teamId', 'bot_uuid', 'search'],
      ),

      // Get by thread ID cache
      this.broker.cacher.getCacheKey(
        'customerContact.actionGetByThreadId',
        {
          bot_uuid: customerContact.bot_uuid,
          thread_id: '*',
        },
        meta,
        ['bot_uuid', 'thread_id'],
      ),
    ];

    this.broker.cacher.clean(cacheKeys);
  }
}

export default CustomerContactService;
