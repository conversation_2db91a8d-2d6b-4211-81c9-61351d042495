'use strict';
import { getModelForClass } from '@typegoose/typegoose';
import axios from 'axios';
import { ReadStream } from 'fs';
import _ from 'lodash';
import { Action, Event, Method, Service as DService } from 'moleculer-decorators';
import * as MoleculerTs from 'moleculer-ts';
import path from 'path';
import { RequireRoles } from '../../commons/annotation.helper';
import { CommonConfig } from '../../commons/common.config';
import { CommonErrors } from '../../commons/error.helper';
import { DATASET_STATUS, DATASET_TYPE, IDataset } from '../../entities/dataset.entity';
import { getVectorCollectionName } from '../../entities/vectorStore.entity';
import { DbBaseMixin, DbBaseMixinActionTypes } from '../../mixins';
import ConfigMixin from '../../mixins/config.mixin';
import { Dataset, DatasetSchema, DatasetType } from '../../models/dataset';
import { ApiGatewayMeta, DatasetServiceTypes, MoleculerDBService } from '../../types';
import { Context, eventName } from '../../types/moleculer';
import CommonUtils from '../../utils/CommonUtils';
import { FileStreamMeta, getPromiseFileSteam } from '../../utils/FileUploadUtils';
import { convertPaginateOptions, convertPaginateQuery } from '../../utils/mongoPlugins/paginate';
import { OwnActions } from './dataset.service.types';

const dbBaseMixin = new DbBaseMixin({
  dbUri: CommonConfig.DB_URI,
  name: 'dbProductMixin',
  collection: 'dataset',
  model: getModelForClass(Dataset),
});
const allowUpdateFields = _.pick(DatasetSchema, ['_id', 'name', 'description', 'vectorConfig']);
Object.keys(allowUpdateFields).forEach((key) => {
  // @ts-ignore
  allowUpdateFields[key].optional = true;
});
// @ts-ignore
allowUpdateFields['$$strict'] = true;

@DService({
  name: DatasetServiceTypes.name,
  mixins: [ConfigMixin(['dataset.**']), dbBaseMixin.getMixin(async (adapter) => {}, [])],
  settings: {
    fields: ['_id', 'name', 'description', 'type', 'status', 'vectorConfig', 'team_id'],
    listFields: ['_id', 'name', 'description', 'type', 'status'],
    tempUploadDir: path.join('tmp', 'dataset'),
  },
})
class DatasetService
  extends MoleculerDBService<
    {
      fields: string[];
      listFields: string[];
      tempUploadDir: string;
    },
    DatasetType
  >
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes>
{
  dependencies: ['vectorStore', 'model768'];

  @RequireRoles()
  @Action({
    rest: 'POST /list',
    cache: {
      keys: ['#user.teamId', 'type', 'search', 'sort', 'page', 'pageSize'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true },
      pageSize: { type: 'number', integer: true, min: 0, optional: true, convert: true },
      sort: { type: 'string', optional: true },
      search: { type: 'string', optional: true },
      type: {
        type: 'number',
        integer: true,
        min: DATASET_TYPE.KNOWLEDGE,
        max: DATASET_TYPE.VECTOR_STORE,
        required: true,
        default: 1,
      },
      // searchFields: { type: 'array', optional: true, items: 'string' },
      // query: { type: 'object', optional: true },
    },
    openapi: {
      description: 'List dataset. Type can be knowledge(1) or qna(2) or vector store(3)',
    },
  })
  async actionList(
    ctx: Context<DatasetServiceTypes.ActionParams<'actionList'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'actionList'>> {
    return this.adapter.model.paginate(
      convertPaginateQuery({
        searchFields: ['name'],
        ...ctx.params,
        query: { type: ctx.params.type, team_id: ctx.meta.user.teamId },
      }),
      convertPaginateOptions(
        { ...ctx.params, fields: this.settings.listFields },
        this.adapter.model.defaultPaginateOptions,
      ),
    );
  }

  @RequireRoles()
  @Action({
    rest: 'POST /create',
    params: {
      ..._.omit(DatasetSchema, ['_id', 'status', 'model_name', 'team_id', '$$strict']),
      type: {
        type: 'number',
        integer: true,
        min: DATASET_TYPE.KNOWLEDGE,
        max: DATASET_TYPE.VECTOR_STORE,
        optional: true,
      },
      vectorConfig: {
        type: 'object',
        optional: true,
        props: {
          fields: { type: 'array', items: 'string' },
          vectorFields: { type: 'array', items: 'string' },
          parseFields: { type: 'object' },
          indexField: { type: 'string', optional: true },
        },
      },
    },
    openapi: {
      description: 'Create new dataset. Type can be knowledge(1) or qna(2) or vector store(3)',
    },
  })
  async actionCreate(
    ctx: Context<DatasetServiceTypes.ActionParams<'actionCreate'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'actionCreate'>> {
    const data: IDataset = {
      ...ctx.params,
      // Set default value
      model_name: 'base768',
      team_id: ctx.meta.user.teamId,
      status: DATASET_STATUS.ACTIVE,
      vectorConfig: ctx.params.vectorConfig,
    };
    if (ctx.params.type === DATASET_TYPE.KNOWLEDGE) {
      data.vectorConfig = { fields: ['data', 'index'], vectorFields: ['data'], indexField: 'index' };
    } else if (ctx.params.type === DATASET_TYPE.QNA) {
      data.vectorConfig = {
        fields: ['data', 'question', 'index'],
        vectorFields: ['data', 'question'],
        indexField: 'index',
      };
    }

    // Get current team package to check knowledge limit
    const teamPackage = await ctx.broker.call('teamPackage.actionGetCurrent', {}, { parentCtx: ctx });
    if (!teamPackage) {
      throw new CommonErrors.NotFoundError('No active package found for team. Please upgrade your subscription!');
    }

    // Check knowledge limit
    const currentKnowledgeCount = await this.adapter.model.countDocuments({
      team_id: ctx.meta.user.teamId,
      type: DATASET_TYPE.KNOWLEDGE,
      status: DATASET_STATUS.ACTIVE,
    });

    if (currentKnowledgeCount >= (teamPackage.knowledgeLimit || 0)) {
      throw new CommonErrors.ValidationError(
        `Team has reached knowledge limit ${currentKnowledgeCount}/${teamPackage.knowledgeLimit}! Please upgrade your package to increase knowledge limit.`,
      );
    }

    data.vectorConfig = checkVectorConfig(data.vectorConfig);

    const result = await this.adapter.insert(data);
    const doc = (await this.transformDocuments(ctx, {}, result)) as IDataset;

    const collectionName = getVectorCollectionName(doc._id);
    await this.broker.call('vectorStore.createCollection', { collectionName }, { parentCtx: ctx });
    this.broker.broadcast('dataset.createDataset', doc);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'PUT /update',
    params: allowUpdateFields,
  })
  async actionUpdate(
    ctx: Context<DatasetServiceTypes.ActionParams<'actionUpdate'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'actionUpdate'>> {
    let data = await this.adapter.model.findOne({ _id: ctx.params._id, team_id: ctx.meta.user.teamId });
    if (!data) {
      throw new CommonErrors.NotFoundError('Dataset not found');
    }
    data = _.merge(data, {
      ...ctx.params,
    });
    if (data.type === DATASET_TYPE.VECTOR_STORE) {
      data.vectorConfig = {
        ...data.vectorConfig,
        ...ctx.params.vectorConfig,
      };
    }
    data.vectorConfig = checkVectorConfig(data.vectorConfig);

    await data.save();

    const doc = (await this.transformDocuments(ctx, {}, data)) as IDataset;
    this.broker.broadcast('dataset.updateDataset', doc);
    return doc;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /get',
    cache: {
      keys: ['#user.teamId', '_id'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
    },
  })
  async actionGet(
    ctx: Context<DatasetServiceTypes.ActionParams<'actionGet'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'actionGet'>> {
    const data = await this.adapter.findOne({ _id: ctx.params._id, team_id: ctx.meta.user.teamId });
    if (!data) {
      throw new CommonErrors.NotFoundError('Dataset not found');
    }
    return (await this.transformDocuments(ctx, {}, data)) as IDataset;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /delete',
    params: {
      $$strict: true,
      _id: { type: 'string', optional: false },
    },
  })
  async actionDelete(
    ctx: Context<DatasetServiceTypes.ActionParams<'actionDelete'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'actionDelete'>> {
    const doc = await this.adapter.model.findOne({ _id: ctx.params._id, team_id: ctx.meta.user.teamId }).lean({ parseId: true });
    if (!doc) {
      throw new CommonErrors.NotFoundError('Dataset not found');
    }
    await this.adapter.removeMany({ _id: ctx.params._id });
    this.broker.broadcast('dataset.removeDataset', doc as IDataset);
    return true;
  }

  @RequireRoles()
  @Action({
    params: {
      // searchEmbedding: { type: 'array', items: { type: 'number' } },
      // knowledge_uuid: { type: 'string', optional: false },
      // question_knowledge_uuid: { type: 'string', optional: false },
      // limit: { type: 'number', optional: true, convert: true },
    },
  })
  async retrieveKnowledge(
    ctx: Context<DatasetServiceTypes.ActionParams<'retrieveKnowledge'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'retrieveKnowledge'>> {
    const { searchEmbedding, knowledge_uuid, question_knowledge_uuid } = ctx.params;
    const limit = ctx.params.limit || 2; // Default limit to 2 if not provided

    const listUUIDQuery = [
      {
        _id: knowledge_uuid,
        type: DATASET_TYPE.KNOWLEDGE,
        dataFields: ['data'],
      },
      {
        _id: question_knowledge_uuid,
        type: DATASET_TYPE.QNA,
        dataFields: ['data', 'question'],
      },
    ].filter((item) => item._id);
    // Fetch both datasets in a single query
    const datasets = await this.adapter.model
      .find({
        $or: listUUIDQuery,
      })
      .select(['_id'])
      .lean({ parseId: true });

    // Check if both UUIDs are valid
    if (listUUIDQuery.length !== datasets.length) {
      // TODO: Need enable check knowledge before run
      this.broker.logger.warn(
        `Knowledge UUID ${knowledge_uuid} or Question Knowledge UUID ${question_knowledge_uuid} is not valid`,
      );
      // throw new CommonErrors.NotFoundError(
      //   `Knowledge UUID ${knowledge_uuid} or Question Knowledge UUID ${question_knowledge_uuid} is not valid`,
      // );
    }

    // Use the embedding to search for related knowledge
    const results = await Promise.all(
      listUUIDQuery.map((dataset) =>
        this.broker.call(
          'vectorStore.search',
          {
            collectionName: getVectorCollectionName(dataset._id),
            vector: searchEmbedding,
            dataFields: dataset.dataFields,
            limit,
          },
          { parentCtx: ctx },
        ),
      ),
    );

    // Combine and return the list of knowledge data
    return results.flat().map((result) => result.data);
  }

  @RequireRoles()
  @Action({
    params: {},
    openapi: {
      description: 'Import knowledge records from a CSV file',
      requestBody: {
        content: {
          'multipart/form-data': {
            schema: {
              type: 'object',
              properties: {
                file: {
                  type: 'string',
                  format: 'binary',
                  description: 'The file to upload',
                },
                dataset_id: {
                  type: 'string',
                  description: 'Id of dataset',
                },
              },
            },
          },
        },
        required: true,
      },
    },
  })
  async actionImportKnowledge(
    ctx: Context<ReadStream, FileStreamMeta<{ dataset_id: string }> & ApiGatewayMeta>,
  ): Promise<{ success: boolean; message: string }> {
    if (ctx.meta.mimetype !== 'text/csv') {
      ctx.params.destroy();
      throw new Error('Only CSV file is supported');
    }

    const datasetId = ctx.meta.$multipart?.dataset_id;
    if (!datasetId) {
      ctx.params.destroy();
      throw new CommonErrors.BadRequestError('Dataset ID is required');
    }

    const dataset = await this.adapter.model.findOne({ _id: datasetId, team_id: ctx.meta.user.teamId });
    if (!dataset) {
      ctx.params.destroy();
      throw new CommonErrors.NotFoundError('Dataset not found');
    }

    const filePath = path.join(this.settings.tempUploadDir, `uploaded_csv_${Date.now()}.csv`);

    return getPromiseFileSteam(ctx.params, filePath, 10 * 1024 * 1024).then(async ({ filePath, bytesRead }) => {
      this.logger.info(`File dataset uploaded: ${filePath}`, bytesRead);
      const result = await this.broker.call(
        'vectorStore.processCsv',
        {
          filePath,
          bytes: bytesRead,
          dataset_id: datasetId,
        },
        { parentCtx: ctx },
      );

      // Broadcast update event after import
      this.broker.broadcast('dataset.updateDataset', (await this.transformDocuments(ctx, {}, dataset)) as IDataset);
      return result;
    });
  }

  @RequireRoles()
  @Action({
    rest: 'POST /cleanKnowledge',
    params: {
      dataset_id: { type: 'string' },
    },
    openapi: {
      description: 'Clean dataset',
    },
  })
  async cleanKnowledge(
    ctx: Context<DatasetServiceTypes.ActionParams<'cleanKnowledge'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'cleanKnowledge'>> {
    const { dataset_id } = ctx.params;

    // Fetch the dataset object by dataset_id
    const dataset = await this.adapter.model.findOne({ _id: dataset_id, team_id: ctx.meta.user.teamId });
    if (!dataset) {
      throw new CommonErrors.NotFoundError('Dataset not found');
    }

    const collectionName = getVectorCollectionName(dataset_id);
    const result = await this.broker.call(
      'vectorStore.cleanCollection',
      { collectionName },
      {
        parentCtx: ctx,
      },
    );

    if (result.success) {
      // Broadcast update event after cleaning
      this.broker.broadcast('dataset.updateDataset', (await this.transformDocuments(ctx, {}, dataset)) as IDataset);
      return { success: true, message: `Dataset '${dataset.name}' cleaned successfully.` };
    } else {
      return result;
    }
  }

  @RequireRoles()
  @Action({
    rest: 'POST /createKnowledge',
    params: {
      dataset_id: { type: 'string' },
      data: { type: 'object' },
    },
    openapi: {
      description: 'Create knowledge record in dataset_id',
    },
  })
  async createKnowledge(
    ctx: Context<DatasetServiceTypes.ActionParams<'createKnowledge'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'createKnowledge'>> {
    const { dataset_id, data } = ctx.params;

    // Fetch the dataset object by dataset_id
    const dataset = await this.adapter.model.findOne({ _id: dataset_id, team_id: ctx.meta.user.teamId });
    if (!dataset) {
      throw new CommonErrors.NotFoundError('Dataset not found');
    }

    // Filter data to include only fields defined in dataset.vectorConfig.fields
    const filteredData = _.pick(data, dataset.vectorConfig.fields);

    // Generate hash for filteredData
    filteredData['hash'] = CommonUtils.generateCustomHashUUID(JSON.stringify(filteredData));

    // Check if indexField is set and use id as id
    let withId = undefined;
    if (dataset.vectorConfig.indexField) {
      const indexFieldValue = data[dataset.vectorConfig.indexField];
      if (indexFieldValue) {
        withId = CommonUtils.generateCustomHashUUID(indexFieldValue);
      }
    }

    // Check if all required fields are present in the filtered data
    const missingFields = dataset.vectorConfig.fields.filter((field) => !(field in filteredData));
    if (missingFields.length > 0) {
      throw new CommonErrors.UnprocessableError([
        {
          field: 'data',
          message: `Missing required fields: ${missingFields.join(', ')}`,
          type: 'required',
        },
      ]);
    }

    const collectionName = getVectorCollectionName(dataset_id);
    try {
      const result = await this.broker.call(
        'vectorStore.create',
        {
          collectionName,
          withId, // Use the hash as the id if indexField is set
          data: filteredData,
          vectorFields: dataset.vectorConfig.vectorFields,
        },
        { parentCtx: ctx },
      );

      // Broadcast create event after creation
      this.broker.broadcast('dataset.createDataset', (await this.transformDocuments(ctx, {}, dataset)) as IDataset);
      return { data: result.data };
    } catch (error) {
      if (error?.message?.startsWith('Duplicate')) {
        throw error;
      }
      this.logger.error(`Error creating knowledge record in dataset ${dataset_id}:`, error);
      throw new Error(`Failed to create knowledge record in dataset ${dataset_id}`);
    }
  }

  @RequireRoles()
  @Action({
    rest: 'POST /importUrl',
    params: {
      dataset_id: { type: 'string', optional: false },
      url: { type: 'string', optional: false },
      tabName: { type: 'string', optional: true },
    },
    openapi: {
      description: 'Import knowledge records from a Google Sheet URL. Url may be a full URL or a sheet ID.',
    },
  })
  async actionImportKnowledgeUrl(
    ctx: Context<DatasetServiceTypes.ActionParams<'actionImportKnowledgeUrl'>, ApiGatewayMeta>,
  ): Promise<{ success: boolean; message: string }> {
    const { dataset_id, url, tabName } = ctx.params;

    // Fetch the dataset object by dataset_id
    const dataset = await this.adapter.model.findOne({ _id: dataset_id, team_id: ctx.meta.user.teamId });
    if (!dataset) {
      throw new CommonErrors.NotFoundError('Dataset not found');
    }

    // Construct the Google Sheet CSV URL
    const sheetId = url.includes('docs.google.com') ? url.split('/d/')[1].split('/')[0] : url;
    const csvUrl = `https://docs.google.com/spreadsheets/d/${sheetId}/gviz/tq?tqx=out:csv&sheet=${tabName}`;

    // Download the CSV file from the URL
    const response = await axios.get(csvUrl, { responseType: 'stream' });

    if (response.status !== 200) {
      throw new CommonErrors.UnprocessableError([], `Failed to download CSV from URL: ${response.statusText}`);
    }

    const filePath = path.join(this.settings.tempUploadDir, `uploaded_csv_${Date.now()}.csv`);

    // Write the CSV data to a file
    return getPromiseFileSteam(response.data, filePath, 10 * 1024 * 1024).then(async ({ filePath, bytesRead }) => {
      this.logger.info(`File dataset uploaded: ${filePath}`, bytesRead);
      const result = await this.broker.call(
        'vectorStore.processCsv',
        {
          filePath,
          bytes: bytesRead,
          dataset_id: dataset_id,
        },
        { parentCtx: ctx },
      );

      // Broadcast update event after import
      this.broker.broadcast('dataset.updateDataset', (await this.transformDocuments(ctx, {}, dataset)) as IDataset);

      return result;
    });
  }

  @RequireRoles()
  @Action({
    rest: 'PUT /updateKnowledge',
    params: {
      dataset_id: { type: 'string' },
      id: { type: 'string' },
      data: { type: 'object' },
    },
    openapi: {
      description: 'Update knowledge record in dataset_id',
    },
  })
  async updateKnowledge(
    ctx: Context<DatasetServiceTypes.ActionParams<'updateKnowledge'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'updateKnowledge'>> {
    const { dataset_id, id, data } = ctx.params;

    // Fetch the dataset object by dataset_id
    const dataset = await this.adapter.model.findOne({ _id: dataset_id, team_id: ctx.meta.user.teamId });
    if (!dataset) {
      throw new CommonErrors.NotFoundError('Dataset not found');
    }

    // Filter data to include only fields defined in dataset.vectorConfig.fields
    const filteredData = _.pick(data, dataset.vectorConfig.fields);

    // Generate hash for filteredData
    const hash = CommonUtils.generateCustomHashUUID(JSON.stringify(filteredData));
    filteredData['hash'] = hash;

    // Check if all required fields are present in the filtered data
    const missingFields = dataset.vectorConfig.fields.filter((field) => !(field in filteredData));
    if (missingFields.length > 0) {
      throw new CommonErrors.UnprocessableError([
        {
          field: 'data',
          message: `Missing required fields: ${missingFields.join(', ')}`,
          type: 'required',
        },
      ]);
    }

    const collectionName = getVectorCollectionName(dataset_id);
    try {
      const result = await this.broker.call(
        'vectorStore.update',
        {
          collectionName,
          id,
          data: filteredData,
          vectorFields: dataset.vectorConfig.vectorFields,
        },
        { parentCtx: ctx },
      );

      // Broadcast update event after update
      this.broker.broadcast('dataset.updateDataset', (await this.transformDocuments(ctx, {}, dataset)) as IDataset);

      return { data: result.data };
    } catch (error) {
      this.logger.error(`Error updating knowledge record in dataset ${dataset_id}:`, error);
      throw new Error(`Failed to update knowledge record in dataset ${dataset_id}`);
    }
  }

  @RequireRoles()
  @Action({
    rest: 'POST /deleteKnowledge',
    params: {
      dataset_id: { type: 'string' },
      ids: { type: 'array', items: 'string' },
    },
    openapi: {
      description: 'Delete knowledge records from dataset_id',
    },
  })
  async deleteKnowledge(
    ctx: Context<DatasetServiceTypes.ActionParams<'deleteKnowledge'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'deleteKnowledge'>> {
    const { dataset_id, ids } = ctx.params;

    // Fetch the dataset object by dataset_id
    const dataset = await this.adapter.model.findOne({ _id: dataset_id, team_id: ctx.meta.user.teamId });
    if (!dataset) {
      throw new CommonErrors.NotFoundError('Dataset not found');
    }

    const collectionName = getVectorCollectionName(dataset_id);
    try {
      const result = await this.broker.call(
        'vectorStore.delete',
        { collectionName, ids },
        {
          parentCtx: ctx,
        },
      );

      // Broadcast remove event after delete
      this.broker.broadcast('dataset.removeDataset', (await this.transformDocuments(ctx, {}, dataset)) as IDataset);
      return { success: true };
    } catch (error) {
      throw new CommonErrors.UnprocessableError([], 'Error while deleting records');
    }
  }

  @RequireRoles()
  @Action({
    rest: 'POST /searchKnowledge',
    cache: {
      keys: ['#user.teamId', 'dataset_id', 'search', 'dataFields', 'limit', 'where', 'sort'],
      ttl: 60 * 5, // 5 minutes for search results
    },
    params: {
      dataset_id: { type: 'string', optional: false },
      search: { type: 'string', optional: false },
      dataFields: { type: 'array', items: 'string', optional: false, min: 1 },
      limit: { type: 'number', optional: true, default: 5, min: 1, max: 10 },
      where: { type: 'object', optional: true },
      sort: { type: 'array', items: 'object', optional: true },
    },
    openapi: {
      description:
        'Search knowledge by dataset_id and check ownership by user. Where sample: { "path": ["points"], "operator": "LessThan", "valueInt": 600 }. Sort sample: { "path": ["points"], "order": "desc" }.',
    },
  })
  async actionSearchKnowledge(
    ctx: Context<DatasetServiceTypes.ActionParams<'actionSearchKnowledge'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'actionSearchKnowledge'>> {
    const { dataset_id, search, dataFields } = ctx.params;

    // Check if the dataset belongs to the user
    const dataset = await this.adapter.model.findOne({ _id: dataset_id, team_id: ctx.meta.user.teamId });
    if (!dataset) {
      throw new CommonErrors.NotFoundError('Dataset not found!');
    }

    // Validate dataFields
    if (dataFields.length < 1) {
      throw new CommonErrors.BadRequestError('dataFields must contain at least one item');
    }
    const specialKeys = ['id', 'distance', 'score'];
    const invalidFields = dataFields.filter(
      (field) => !dataset.vectorConfig.fields.includes(field) && !specialKeys.includes(field),
    );
    if (invalidFields.length > 0) {
      throw new CommonErrors.BadRequestError(`Invalid fields in dataFields: ${invalidFields.join(', ')}`);
    }

    // Convert search field to searchEmbedding using model768.getEmbedding
    let searchEmbedding: number[] = [];
    if (search) {
      const { embeddings } = await this.broker.call(
        'model768.getEmbedding',
        { messages: [search] },
        { parentCtx: ctx },
      );
      searchEmbedding = embeddings[0];
    }

    const collectionName = getVectorCollectionName(dataset_id);
    const { data } = await this.broker.call(
      'vectorStore.search',
      {
        collectionName,
        vector: searchEmbedding,
        dataFields,
        where: ctx.params.where,
        limit: ctx.params.limit,
        sort: ctx.params.sort,
      },
      { parentCtx: ctx },
    );
    return data;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /getKnowledge',
    cache: {
      keys: ['#user.teamId', 'dataset_id', 'page'],
      ttl: 60 * 30, // 30 minutes
    },
    params: {
      dataset_id: { type: 'string', optional: false },
      page: { type: 'number', integer: true, min: 1, default: 1 },
    },
    openapi: {
      description: 'Get knowledge items from dataset_id with paging support',
    },
  })
  async getKnowledge(
    ctx: Context<DatasetServiceTypes.ActionParams<'getKnowledge'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'getKnowledge'>> {
    const { dataset_id, page } = ctx.params;

    const collectionName = getVectorCollectionName(dataset_id);
    const pageSize = 10; // Default page size

    const total = await ctx.broker.call(
      'vectorStore.countCollection',
      {
        collectionName,
      },
      { parentCtx: ctx },
    );
    if (total.total === 0) {
      return { data: [], total: 0, success: true };
    }

    // Fetch the dataset object by dataset_id
    const dataset = await this.adapter.model.findOne({ _id: dataset_id, team_id: ctx.meta.user.teamId });
    if (!dataset) {
      throw new CommonErrors.NotFoundError('Dataset not found');
    }

    const fields = dataset.vectorConfig?.fields || [];
    fields.push('_additional { id }'); // Add Weaviate ID to the fields list
    const result = await ctx.broker.call(
      'vectorStore.getData',
      {
        collectionName,
        page,
        pageSize,
        fields,
      },
      { parentCtx: ctx },
    );

    return { ...result, total: total.total };
  }

  // EVENT HANDLERS
  @Method
  @Event({
    name: eventName('dataset.createDataset'),
  })
  cleanCacheOnCreate(dataset: IDataset) {
    this.cleanCache(dataset);
  }

  @Method
  @Event({
    name: eventName('dataset.updateDataset'),
  })
  cleanCacheOnUpdate(dataset: IDataset) {
    this.cleanCache(dataset);
  }

  @Method
  @Event({
    name: eventName('dataset.removeDataset'),
  })
  cleanCacheOnRemove(dataset: IDataset) {
    this.cleanCache(dataset);
  }

  @RequireRoles()
  @Action({
    rest: 'GET /allTeamDatasetNames',
    cache: {
      keys: ['#user.teamId'],
      ttl: 60 * 10, // 10 minutes
    },
    openapi: {
      description: 'Get all dataset names for current team, separated into knowledge, questions and vectors',
    },
  })
  async getAllTeamDatasetName(
    ctx: Context<DatasetServiceTypes.ActionParams<'getAllTeamDatasetName'>, ApiGatewayMeta>,
  ): Promise<DatasetServiceTypes.ActionReturn<'getAllTeamDatasetName'>> {
    const teamId = ctx.meta.user.teamId;

    const datasets = await this.adapter.model
      .find({
        team_id: teamId,
        status: DATASET_STATUS.ACTIVE,
      })
      .select(['_id', 'name', 'type'])
      .lean({ parseId: true });

    const result = {
      knowledges: datasets.filter((d) => d.type === DATASET_TYPE.KNOWLEDGE),
      questions: datasets.filter((d) => d.type === DATASET_TYPE.QNA),
      vectors: datasets.filter((d) => d.type === DATASET_TYPE.VECTOR_STORE),
    };

    return result;
  }

  private cleanCache(dataset: IDataset) {
    this.broker.logger.info('Dataset cache cleaned', dataset.team_id, dataset._id);
    const meta = { user: { teamId: dataset.team_id } };

    const cacheKeys = [
      // Team dataset names cache
      this.broker.cacher.getCacheKey('dataset.getAllTeamDatasetName', {}, meta, ['#user.teamId']),
      // List cache
      this.broker.cacher.getCacheKey('dataset.actionList', { search: '*' }, meta, ['#user.teamId', 'search']),

      // Get single item cache
      this.broker.cacher.getCacheKey('dataset.actionGet', { _id: dataset._id }, meta, ['#user.teamId', '_id']),

      // Paginated data cache
      this.broker.cacher.getCacheKey('dataset.getKnowledge', { dataset_id: dataset._id, page: '*' }, meta, [
        '#user.teamId',
        'dataset_id',
        'page',
      ]),

      // Search cache
      this.broker.cacher.getCacheKey('dataset.actionSearchKnowledge', { dataset_id: dataset._id }, meta, [
        '#user.teamId',
        'dataset_id',
      ]),
    ];

    this.broker.cacher.clean(cacheKeys);
  }

  // END EVENT HANDLERS

  created() {
    this.waitForServices(['api']);
  }
}

/**
 * Checking vector config -> parse fields is one of 'json' | 'number' | 'boolean'
 */
function checkVectorConfig(vectorConfig: IDataset['vectorConfig']) {
  // Filter out blank items from vectorFields
  if (vectorConfig && vectorConfig.vectorFields) {
    vectorConfig.vectorFields = vectorConfig.vectorFields.filter((field) => field.trim() !== '');
  }
  if (!vectorConfig || !vectorConfig.vectorFields || vectorConfig.vectorFields.length === 0) {
    throw new CommonErrors.UnprocessableError([
      {
        field: 'vectorConfig.vectorFields',
        message: 'Vector config vectorFields is required and must not be empty!',
      },
    ]);
  }

  if (vectorConfig?.parseFields) {
    if (typeof vectorConfig.parseFields !== 'object') {
      throw new CommonErrors.UnprocessableError([
        { field: 'vectorConfig.parseFields', message: 'Vector config parseFields must be an object' },
      ]);
    }

    Object.keys(vectorConfig.parseFields).forEach((key) => {
      if (['json', 'number', 'boolean'].indexOf(vectorConfig.parseFields[key]) === -1) {
        throw new CommonErrors.UnprocessableError([
          {
            field: `vectorConfig.parseFields.${key}`,
            message: `Vector config parseFields.${key} must be 'json' | 'number' | 'boolean'`,
          },
        ]);
      }
    });
  }
  return vectorConfig;
}

export = DatasetService;
