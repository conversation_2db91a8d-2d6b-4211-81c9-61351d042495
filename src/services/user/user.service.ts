'use strict';
// import types from custom Moleculer typedef
import { Context, eventName } from '../../types/moleculer';
// import other services types
import { ApiGatewayMeta, ConfigServiceTypes, MoleculerDBService, UserServiceTypes } from '../../types';
// use moleculer-decorators
import { getModelForClass } from '@typegoose/typegoose';
import admin from 'firebase-admin';
import jwt from 'jsonwebtoken';
import _ from 'lodash';
import { Action, Event, Method, Service as DService } from 'moleculer-decorators';
import * as MoleculerTs from 'moleculer-ts';
import { RequireRoles } from '../../commons/annotation.helper';
import { CommonConfig } from '../../commons/common.config';
import { CommonErrors } from '../../commons/error.helper';
import { STATUSES } from '../../entities/base.entity';
import { IUser, PERMISSIONS, USER_ROLE } from '../../entities/user.entity';
import { DbBaseMixin, DbBaseMixinActionTypes } from '../../mixins';
import ConfigMixin, { DefaultConfigType } from '../../mixins/config.mixin';
import { User, UserSchema, UserType } from '../../models/user';
import { convertPaginateOptions, convertPaginateQuery } from '../../utils/mongoPlugins/paginate';
import { OwnActions } from './user.service.types';

const dbBaseMixin = new DbBaseMixin({
  dbUri: CommonConfig.DB_URI,
  name: 'dbProductMixin',
  collection: 'user',
  model: getModelForClass(User),
});
const allowUpdateFields = _.pick(UserSchema, ['name', 'password']);
Object.keys(allowUpdateFields).forEach((key) => {
  // @ts-ignore
  allowUpdateFields[key].optional = true;
});
// @ts-ignore
allowUpdateFields['$$strict'] = true;

@DService({
  name: UserServiceTypes.name,
  mixins: [
    ConfigMixin(['users.**', 'config.firebase_admin']),
    dbBaseMixin.getMixin(async (adapter) => {
      if ((await adapter.count()) === 0) {
        // For admin user for first time
        const user = new User();
        user.name = 'Admin';
        user.username = '<EMAIL>';
        user.password = 'Admin@@123';
        user.role = USER_ROLE.ADMIN;
        user.status = STATUSES.ACTIVE;

        // Add all permission to first user
        user.permissions = PERMISSIONS;

        await adapter
          .insert(user)
          .then((value) => {
            // @ts-ignore
            adapter.broker.logger.info('First start admin account!');
          })
          .catch((reason) => {
            // @ts-ignore
            adapter.broker.logger.warn('Error when create first user!', reason);
          });
      }
    }, []),
  ],
  settings: {
    JWT_SECRET: CommonConfig.JWT_SECRET,
    fields: ['_id', 'username', 'name', 'permissions', 'teamId', 'role', 'status'],
    defaultTeamPackageId: '',
  },
})
class UserService
  extends MoleculerDBService<
    {
      JWT_SECRET: string;
      fields: string[];
      defaultTeamPackageId: string;
    },
    UserType
  >
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes>
{
  config: DefaultConfigType = {
    'users.defaultRole': USER_ROLE.USER,
  };

  firebaseAdmin: admin.app.App = undefined;

  @Action({
    rest: 'POST /register',
    params: {
      ..._.omit(UserSchema, ['_id', 'role', 'permissions', 'status', '$$strict']),
      teamId: { optional: true, type: 'string' },
    },
  })
  async actionRegister(
    ctx: Context<UserServiceTypes.ActionParams<'actionRegister'>>,
  ): Promise<UserServiceTypes.ActionReturn<'actionRegister'>> {
    if (this.config['users.signup.enabled']) {
      const data: IUser = {
        ...ctx.params,
        // Set default value
        permissions: [],
        role: this.config['users.defaultRole'],
        status: this.settings.defaultTeamPackageId ? STATUSES.ACTIVE : STATUSES.PENDING,
      };

      // Check existed email
      const existedUser = await this.adapter.findOne({
        username: data.username,
      });
      if (existedUser) {
        throw new CommonErrors.UnprocessableError([
          {
            field: 'username',
            message: 'Email is existed!',
          },
        ]);
      }

      // Check valid team id
      if (ctx.params.teamId) {
        // Check valid team id is valid object id string
        // @ts-ignore
        if (ctx.params.teamId.length !== 24) {
          throw new CommonErrors.UnprocessableError([
            {
              field: 'teamId',
              message: 'Team id is invalid!',
            },
          ]);
        }
        // Get user by team id using adapter
        const team = await this.adapter.findOne({
          _id: data.teamId,
        });
        if (!team) {
          throw new CommonErrors.UnprocessableError([
            {
              field: 'teamId',
              message: 'Team not found!',
            },
          ]);
        }
      }

      await User.checkStrongPassword(data.password);

      const user = await this.adapter.insert(data);

      let result = 'Bạn đã đăng ký thành công vui lòng chờ quản trị viên xác nhận!';
      // Add user team package if user is owner
      if (this.settings.defaultTeamPackageId && !user.teamId) {
        await this.broker.call(
          'teamPackage.actionAssign',
          {
            teamId: user._id.toString(),
            packageId: this.settings.defaultTeamPackageId,
          },
          { parentCtx: ctx },
        );
        result = 'Đăng ký tài khoản thành công!';
      }

      const doc = (await this.transformDocuments(ctx, {}, user)) as IUser;
      this.broker.broadcast('user.created', doc);

      return result;
    } else {
      throw new CommonErrors.BadRequestError('Sign up process is disabled! Please contact admin to sign up!');
    }
  }

  @Action({
    rest: 'POST /login',
    params: _.pick(UserSchema, ['username', 'password']),
  })
  async actionLogin(
    ctx: Context<UserServiceTypes.ActionParams<'actionLogin'>, ApiGatewayMeta>,
  ): Promise<UserServiceTypes.ActionReturn<'actionLogin'>> {
    const { username, password } = ctx.params;

    const result = await this.adapter.model.findOne({ username }).lean({ parseId: true });

    if (!result) {
      throw new CommonErrors.NotFoundError('User/password is invalid!');
    } else {
      // Check valid password
      if (!(await User.comparePassword(result, password))) {
        throw new CommonErrors.NotFoundError('User/password is invalid!');
      }

      if (result.status == STATUSES.PENDING) {
        throw new CommonErrors.NotFoundError('Please activate user before login!');
      } else if (result.status != STATUSES.ACTIVE) {
        throw new CommonErrors.NotFoundError('This user unable to login! Please contact to admin!');
      }
    }

    const user = _.pick(result, this.settings.fields);
    const token = this.generateJWT(user, ctx.meta.clientIp);

    this.broker.broadcast('user.login', { user, token });

    ctx.meta.$responseHeaders = { Authorization: `Bearer ${token}` };
    return { token, user };
  }

  @Action({
    rest: 'POST /firebaseLogin',
    params: { firebaseToken: 'string' },
  })
  async actionFirebaseLogin(
    ctx: Context<UserServiceTypes.ActionParams<'actionFirebaseLogin'>, ApiGatewayMeta>,
  ): Promise<UserServiceTypes.ActionReturn<'actionFirebaseLogin'>> {
    const { firebaseToken } = ctx.params;

    if (!this.firebaseAdmin) {
      throw new CommonErrors.NotFoundError('System not support firebase yet! Please contact to admin!');
    }

    let result = undefined;
    try {
      const decodedToken = await this.firebaseAdmin.auth().verifyIdToken(firebaseToken);
      if (decodedToken.email) {
        result = await this.adapter.model.findOne({ username: decodedToken.email }).lean({ parseId: true });
      }
    } catch (e) {
      throw new CommonErrors.NotFoundError('Firebase token is invalid');
    }

    if (!result) {
      throw new CommonErrors.NotFoundError('User is invalid or not in the system!');
    } else {
      if (result.status == STATUSES.PENDING) {
        throw new CommonErrors.NotFoundError('Please activate user before login!');
      } else if (result.status != STATUSES.ACTIVE) {
        throw new CommonErrors.NotFoundError('This user unable to login! Please contact to admin!');
      }
    }

    const user = _.pick(result, this.settings.fields);
    const token = this.generateJWT(user, ctx.meta.clientIp);

    this.broker.broadcast('user.login', { user, token });

    ctx.meta.$responseHeaders = { Authorization: `Bearer ${token}` };
    return { token, user };
  }

  @RequireRoles([])
  @Action({
    rest: 'POST /logout',
  })
  async actionLogout(
    ctx: Context<UserServiceTypes.ActionParams<'actionLogout'>, ApiGatewayMeta>,
  ): Promise<UserServiceTypes.ActionReturn<'actionLogout'>> {
    const token = ctx.meta.token;
    if (ctx.meta.user?._id) {
      this.broker.broadcast('user.logout', {
        user: ctx.meta.user,
        token,
      });
    }
    return ctx.meta.user?._id ? 'Bạn đã đăng xuất thành công!' : 'Có lỗi trong quá trình đăng xuất!';
  }

  @Action({
    rest: 'POST /verifyToken',
    // visibility: 'public',
    cache: {
      keys: ['token'],
      ttl: 10 * 60, // 10 mins
    },
  })
  async actionVerifyToken(
    ctx: Context<UserServiceTypes.ActionParams<'actionVerifyToken'>>,
  ): Promise<UserServiceTypes.ActionReturn<'actionVerifyToken'>> {
    const decoded: any = await new this.Promise((resolve, reject) => {
      jwt.verify(ctx.params.token, this.settings.JWT_SECRET, (err: any, decoded: unknown) => {
        if (err) {
          ctx.broker.logger.warn('Error when trying decode token!', err?.message);
          // Need log but not throw error. Only return null
          // return reject(err);
        }
        resolve(decoded);
      });
    });

    if (decoded && decoded._id) {
      const user = await this.adapter.model.findById(decoded._id).select(this.settings.fields).lean({ parseId: true });

      // Only active user can access
      if (user?.status == STATUSES.ACTIVE) {
        // Fill teamId if not valid
        user.teamId = user.teamId || user._id;
        return user;
      }
    }
    return null;
  }

  @RequireRoles()
  @Action({
    rest: 'POST /me',
    cache: {
      keys: ['#user._id'], // Only user ID needed
      ttl: 15 * 60,
    },
  })
  async actionMe(
    ctx: Context<UserServiceTypes.ActionParams<'actionMe'>, ApiGatewayMeta>,
  ): Promise<UserServiceTypes.ActionReturn<'actionMe'>> {
    return this.adapter.model.findById(ctx.meta?.user?._id).select(this.settings.fields).lean({ parseId: true });
  }

  @RequireRoles()
  @Action({
    rest: 'PUT /me',
    params: allowUpdateFields,
  })
  async actionMeUpdate(
    ctx: Context<UserServiceTypes.ActionParams<'actionMeUpdate'>, ApiGatewayMeta>,
  ): Promise<UserServiceTypes.ActionReturn<'actionMeUpdate'>> {
    let user = await this.adapter.model.findById(ctx.meta?.user?._id);
    user = _.merge(user, ctx.params);

    if (!_.isEmpty(ctx.params.password)) {
      await User.checkStrongPassword(ctx.params.password);

      user.password = await User.hashPassword(ctx.params.password);
    }
    await user.save();

    const doc = (await this.transformDocuments(ctx, {}, user)) as IUser;
    this.broker.broadcast('user.updated', doc);
    return doc;
  }

  @RequireRoles([USER_ROLE.ADMIN, USER_ROLE.MODERATOR])
  @Action({
    rest: 'POST /list',
    // cache: {
    //   keys: ['#user.teamId', 'search', 'sort', 'page', 'pageSize'], // Add proper cache keys
    //   ttl: 60 * 30, // 30 minutes
    // },
    params: {
      $$strict: true,
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true },
      pageSize: { type: 'number', integer: true, min: 0, optional: true, convert: true },
      sort: { type: 'string', optional: true },
      search: { type: 'string', optional: true },
      // searchFields: { type: 'array', optional: true, items: 'string' },
      // query: { type: 'object', optional: true },
    },
  })
  async actionList(
    ctx: Context<UserServiceTypes.ActionParams<'actionList'>, ApiGatewayMeta>,
  ): Promise<UserServiceTypes.ActionReturn<'actionList'>> {
    return this.adapter.model.paginate(
      {
        status: { $ne: STATUSES.DELETED },
        ...convertPaginateQuery({ searchFields: ['name', 'username'], ...ctx.params }),
      },
      convertPaginateOptions(
        {
          ...ctx.params,
          fields: this.settings.fields,
          populate: [
            {
              path: 'teamPackage',
              select: '-__v',
            },
          ],
          lean: { parseId: true },
        },
        this.adapter.model.defaultPaginateOptions,
      ),
    );
  }

  @RequireRoles([USER_ROLE.ADMIN, USER_ROLE.MODERATOR])
  @Action({
    rest: 'PUT /updateUserStatus',
    params: {
      userId: { type: 'string' },
      status: { type: 'number' },
    },
    openapi: {
      description: 'Update the status of a user. Only users with ADMIN or MODERATOR roles can perform this action.',
      requestBody: {
        content: {
          'application/json': {
            schema: {
              type: 'object',
              example: {
                userId: '60d5f7a0e4b0a8b3e8f1c9a1',
                status: 1,
              },
            },
          },
        },
      },
    },
  })
  async updateUserStatus(
    ctx: Context<UserServiceTypes.ActionParams<'updateUserStatus'>, ApiGatewayMeta>,
  ): Promise<UserServiceTypes.ActionReturn<'updateUserStatus'>> {
    const { userId, status } = ctx.params;

    if (!Object.values(STATUSES).includes(status)) {
      throw new CommonErrors.UnprocessableError([
        {
          field: 'status',
          message: 'Status must be one of the allowed values.',
        },
      ]);
    }

    // Get target user to check their role
    const targetUser = await this.adapter.model.findById(userId).lean({ parseId: true });
    if (!targetUser) {
      throw new CommonErrors.NotFoundError('User not found!');
    }

    // Only allow if current user has higher role than target user
    if (ctx.meta.user?.role <= targetUser.role) {
      throw new CommonErrors.ForbiddenError("You do not have sufficient privileges to change this user's status");
    }

    const user = await this.adapter.model
      .findByIdAndUpdate(
        userId,
        { status },
        {
          new: true,
          fields: this.settings.fields,
        },
      )
      .lean({ parseId: true });

    if (!user) {
      throw new CommonErrors.NotFoundError('User not found!');
    }

    this.broker.broadcast('user.updated', user);

    return user;
  }

  @Event({
    name: eventName('user.login'),
  })
  async userLogin(payload: UserServiceTypes.EventParams<'login'>) {
    if (payload?.user?._id) {
      const userId = payload.user._id;
      const teamId = payload.user.teamId || userId;
      const meta = { user: { teamId, _id: userId } };

      // Update user-specific caches
      const cacheKeys = [
        this.broker.cacher.getCacheKey('user.actionMe', {}, meta, ['#user._id']),
        this.broker.cacher.getCacheKey('user.actionVerifyToken', { token: '*' }, meta, ['token']),
      ];

      await Promise.all([this.broker.cacher.set(cacheKeys[0], payload.user), this.broker.cacher.clean(cacheKeys[1])]);
    }
  }

  @Event({
    name: eventName('user.logout'),
  })
  async userLogout(payload: UserServiceTypes.EventParams<'logout'>) {
    if (payload?.user?._id) {
      const userId = payload.user._id;
      const teamId = payload.user.teamId || userId;
      const meta = { user: { teamId, _id: userId } };

      const cacheKeys = [
        this.broker.cacher.getCacheKey('user.actionMe', {}, meta, ['#user._id']),
        this.broker.cacher.getCacheKey('user.actionVerifyToken', { token: payload.token }, meta, ['token']),
      ];

      await this.broker.cacher.clean(cacheKeys);
    }
  }

  @Method
  @Event({
    name: eventName('user.removed'),
  })
  cleanCacheOnRemove(user: IUser) {
    this.cleanUserCache(user);
  }

  @Method
  @Event({
    name: eventName('user.updated'),
  })
  cleanCacheOnUpdate(user: IUser) {
    this.cleanUserCache(user);
  }

  @Method
  @Event({
    name: eventName('user.created'),
  })
  cleanCacheOnCreate(user: IUser) {
    this.cleanUserCache(user);
  }

  private cleanUserCache(user: IUser) {
    const userId = user._id;
    const teamId = user.teamId || userId;
    const meta = { user: { teamId, _id: userId } };

    const cacheKeys = [
      // User-specific caches
      this.broker.cacher.getCacheKey('user.actionMe', {}, meta, ['#user._id']),
      this.broker.cacher.getCacheKey('user.actionVerifyToken', { token: '*' }, meta, ['token']),

      // Team-specific caches
      this.broker.cacher.getCacheKey('user.actionList', { search: '*' }, meta, ['#user.teamId', 'search']),
      this.broker.cacher.getCacheKey('user.updateUserStatus', { userId }, meta, ['userId']),
    ];

    this.broker.cacher.clean(cacheKeys);
  }

  @Method
  generateJWT(user: IUser, clientIp: string) {
    const exp = new Date();
    const nextHours = this.config['users.jwt.expiresIn'] >= 1 ? this.config['users.jwt.expiresIn'] : 24;
    exp.setHours(exp.getHours() + nextHours);

    return jwt.sign(
      {
        ...user,
        teamId: user.teamId || user._id,
        clientIp,
        exp: Math.floor(exp.getTime() / 1000),
      },
      this.settings.JWT_SECRET,
    );
  }

  async configChanged(payload: ConfigServiceTypes.EventParams<'changed'>) {
    payload.forEach((config) => {
      if (config.key === 'config.firebase_admin') {
        try {
          const serviceAccount = JSON.parse(config.value);
          this.firebaseAdmin = admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
          });
          this.logger.info('Fireabase admin have been logged!');
        } catch (e) {
          this.logger.error('Firebase config error!', e.message);
        }
      } else if (config.key === 'users.default_team_package_id') {
        this.settings.defaultTeamPackageId = config.value;
      }
    });
  }

  async started() {
    await this.waitForServices(['api', 'config']);
  }
}

export = UserService;
