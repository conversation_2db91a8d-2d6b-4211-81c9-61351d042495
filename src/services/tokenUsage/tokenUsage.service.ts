'use strict';
import { Context } from '../../types/moleculer';
import { ApiGatewayMeta, ConfigServiceTypes, MoleculerDBService, TokenUsageServiceTypes } from '../../types';
import { Action, Service as DService } from 'moleculer-decorators';
import { DbBaseMixin, DbBaseMixinActionTypes } from '../../mixins';
import { CommonConfig } from '../../commons/common.config';
import { getModelForClass } from '@typegoose/typegoose';
import * as MoleculerTs from 'moleculer-ts';
import { OwnActions } from './tokenUsage.service.types';
import ConfigMixin from '../../mixins/config.mixin';
import { TokenUsage, TokenUsageType } from '../../models/tokenUsage';
import { RequireRoles } from '../../commons/annotation.helper';
import { CommonErrors } from '../../commons/error.helper';
import { USER_ROLE } from '../../entities/user.entity';
import { BotTokenUsage, TokenStats } from '../../entities/tokenUsage.entity';
import { ITokenUsageDaily } from 'entities/tokenUsageDaily.entity';

const dbBaseMixin = new DbBaseMixin({
  dbUri: CommonConfig.DB_URI,
  name: 'dbTokenUsageMixin',
  collection: 'tokenUsage',
  model: getModelForClass(TokenUsage),
});

@DService({
  name: TokenUsageServiceTypes.name,
  mixins: [ConfigMixin(['tokenUsage.**']), dbBaseMixin.getMixin(async (adapter) => {}, [])],
  settings: {
    fields: ['_id', 'teamId', 'month', 'messages', 'promptTokens', 'evalTokens', 'cost', 'bots', 'dailyUsage'],
  },
})
class TokenUsageService
  extends MoleculerDBService<
    {
      fields: string[];
    },
    TokenUsageType
  >
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes>
{
  @RequireRoles()
  @Action({
    rest: 'POST /getMe',
    params: {
      month: { type: 'number', required: true },
      forceUpdate: { type: 'boolean', optional: true },
    },
  })
  async getMe(
    ctx: Context<TokenUsageServiceTypes.ActionParams<'getMe'>, ApiGatewayMeta>,
  ): Promise<TokenUsageServiceTypes.ActionReturn<'getMe'>> {
    const { month } = ctx.params;
    const teamId = ctx.meta.user.teamId;

    const year = Math.floor(month / 100);
    const monthNumber = month % 100;

    const tokenUsage = await this.adapter.model.findOne({ teamId, month }).select(this.settings.fields).lean({ parseId: true });
    if (!tokenUsage) {
      throw new CommonErrors.NotFoundError('Token usage not found for the specified month');
    }

    if (!tokenUsage.dailyUsage || ctx.params.forceUpdate) {
      tokenUsage.dailyUsage = {};
      const botUuids = Object.keys(tokenUsage.bots);
      const dailyUsages = await Promise.all(
        botUuids.map((botUuid) =>
          this.broker
            .call(
              'tokenUsageDaily.reportUsage',
              {
                bot_uuid: botUuid,
                start_date: `${year}${String(monthNumber).padStart(2, '0')}01`,
                end_date: `${year}${String(monthNumber).padStart(2, '0')}31`,
              },
              { parentCtx: ctx },
            )
            .then((dailyUsageList: ITokenUsageDaily[]) => {
              return dailyUsageList.map((dailyUsage) => ({
                date: dailyUsage.date.toISOString().split('T')[0],
                hourlyUsage: dailyUsage.hourlyUsage,
              }));
            })
            .catch((error: any) => {
              return [];
            }),
        ),
      );

      tokenUsage.dailyUsage = {};
      dailyUsages.forEach((dailyUsageList) => {
        dailyUsageList.forEach((dailyUsage) => {
          const formattedDate = dailyUsage.date;
          if (!tokenUsage.dailyUsage[formattedDate]) {
            tokenUsage.dailyUsage[formattedDate] = {
              messages: 0,
              promptTokens: 0,
              evalTokens: 0,
              cost: 0,
            };
          }

          for (const hour in dailyUsage.hourlyUsage) {
            if (dailyUsage.hourlyUsage.hasOwnProperty(hour)) {
              tokenUsage.dailyUsage[formattedDate].messages += dailyUsage.hourlyUsage[hour].messages;
              tokenUsage.dailyUsage[formattedDate].promptTokens += dailyUsage.hourlyUsage[hour].promptTokens;
              tokenUsage.dailyUsage[formattedDate].evalTokens += dailyUsage.hourlyUsage[hour].evalTokens;
              tokenUsage.dailyUsage[formattedDate].cost += dailyUsage.hourlyUsage[hour].cost;
            }
          }
        });
      });

      await this.adapter.model.updateOne({ teamId, month }, { $set: { dailyUsage: tokenUsage.dailyUsage } });
    }

    return tokenUsage;
  }

  @RequireRoles([USER_ROLE.ADMIN])
  @Action({
    rest: 'POST /getByTeam',
    params: {
      month: { type: 'number', required: true },
      teamId: { type: 'string', required: true },
    },
  })
  async getByTeam(
    ctx: Context<TokenUsageServiceTypes.ActionParams<'getByTeam'>, ApiGatewayMeta>,
  ): Promise<TokenUsageServiceTypes.ActionReturn<'getByTeam'>> {
    const { month, teamId } = ctx.params;

    const tokenUsage = await this.adapter.model.findOne({ teamId, month }).select(this.settings.fields).lean({ parseId: true });
    if (!tokenUsage) {
      throw new CommonErrors.NotFoundError('Token usage not found for the specified month and team');
    }
    return tokenUsage;
  }

  private initializeTokenStats(): TokenStats {
    return { total: 0, min: Infinity, max: 0, mean: 0 };
  }

  private initializeBotTokenUsage(): BotTokenUsage {
    return {
      promptTokens: this.initializeTokenStats(),
      evalTokens: this.initializeTokenStats(),
      cost: this.initializeTokenStats(),
      messages: 0,
    };
  }

  private updateTokenStats(stats: TokenStats, token: number, messages: number): void {
    stats.total += token;
    stats.min = Math.min(stats.min, token);
    stats.max = Math.max(stats.max, token);
    stats.mean = Math.round(stats.total / messages);
  }

  @Action({
    params: {
      teamId: { type: 'string', required: true },
      bot_uuid: { type: 'string', required: true },
      prompt_token: { type: 'number', required: true },
      eval_token: { type: 'number', required: true },
      cost: { type: 'number', required: true },
    },
  })
  async addTokenUsage(
    ctx: Context<TokenUsageServiceTypes.ActionParams<'addTokenUsage'>, ApiGatewayMeta>,
  ): Promise<TokenUsageServiceTypes.ActionReturn<'addTokenUsage'>> {
    const { teamId, bot_uuid, prompt_token, eval_token, cost } = ctx.params;
    this.broker.logger.info('User spent token:', bot_uuid, teamId, ctx.params);

    // Update team package credit usage
    await this.broker
      .call(
        'teamPackage.actionAddCreditUsage',
        {
          teamId,
          credits: cost,
        },
        { parentCtx: ctx },
      )
      .catch((err) => {
        this.broker.logger.error('Failed to update team package credit usage:', err);
        throw new Error(`Unable to update credits! Error: ${err?.message}`);
      });

    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth() + 1;
    const formattedMonth = year * 100 + month;

    let tokenUsage = await this.adapter.model.findOne({ teamId, month: formattedMonth }).lean({ parseId: true });
    if (!tokenUsage) {
      // @ts-expect-error
      tokenUsage = {
        teamId,
        month: formattedMonth,
        ...this.initializeBotTokenUsage(),
        bots: {},
        dailyUsage: {},
      };
    }

    tokenUsage.messages += 1;
    this.updateTokenStats(tokenUsage.promptTokens, prompt_token, tokenUsage.messages);
    this.updateTokenStats(tokenUsage.evalTokens, eval_token, tokenUsage.messages);
    this.updateTokenStats(tokenUsage.cost, cost, tokenUsage.messages);

    if (!tokenUsage.bots[bot_uuid]) {
      tokenUsage.bots[bot_uuid] = this.initializeBotTokenUsage();
    }

    tokenUsage.bots[bot_uuid].messages += 1;
    this.updateTokenStats(tokenUsage.bots[bot_uuid].promptTokens, prompt_token, tokenUsage.bots[bot_uuid].messages);
    this.updateTokenStats(tokenUsage.bots[bot_uuid].evalTokens, eval_token, tokenUsage.bots[bot_uuid].messages);
    this.updateTokenStats(tokenUsage.bots[bot_uuid].cost, cost, tokenUsage.bots[bot_uuid].messages);

    const day = currentDate.getDate();
    const formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

    if (!tokenUsage.dailyUsage[formattedDate]) {
      tokenUsage.dailyUsage[formattedDate] = {
        messages: 0,
        promptTokens: 0,
        evalTokens: 0,
        cost: 0,
      };
    }

    tokenUsage.dailyUsage[formattedDate].messages += 1;
    tokenUsage.dailyUsage[formattedDate].promptTokens += prompt_token;
    tokenUsage.dailyUsage[formattedDate].evalTokens += eval_token;
    tokenUsage.dailyUsage[formattedDate].cost += cost;

    await this.adapter.model.updateOne({ teamId, month: formattedMonth }, { $set: tokenUsage }, { upsert: true });

    const result = await this.broker.call(
      `tokenUsageDaily.addTokenUsage`,
      {
        bot_uuid,
        prompt_token,
        eval_token,
        cost,
      },
      { parentCtx: ctx },
    );

    return tokenUsage;
  }

  async configChanged(payload: ConfigServiceTypes.EventParams<'changed'>) {
    console.log('TokenUsage event configChanged', payload);
  }

  created() {
    this.waitForServices(['api']);
  }
}

export = TokenUsageService;
