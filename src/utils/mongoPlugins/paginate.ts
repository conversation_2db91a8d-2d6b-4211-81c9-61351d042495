'use strict';
import { QueryOptions } from 'moleculer-db';
import { FilterQ<PERSON>y, <PERSON>, <PERSON>hem<PERSON>, SortOrder } from 'mongoose';
import { ActionListQuery, DBPagination } from '../../types';
import { MongooseLeanDefaultsOptions } from './mongoLeanId';

declare module 'mongoose' {
  // @ts-ignore
  interface Model<T, TQueryHelpers = {}, TMethods = {}> extends NodeJS.EventEmitter, AcceptsDiscriminator {
    // Start mongoose paging plugin
    defaultPaginateOptions: MongoosePaginateOptions;

    paginate(query: FilterQuery<T>, options: MongoosePaginateOptions): Promise<DBPagination<T>>;

    // End mongoose paging plugin
  }
}

export interface MongoosePaginateOptions {
  lean?: boolean | MongooseLeanDefaultsOptions;
  page?: number;
  pageSize?: number;
  select?: string | string[];
  sort?: string | { [key: string]: SortOrder | { $meta: 'textScore' } } | undefined | null; // https://mongoosejs.com/docs/api.html#query_Query-sort
  populate?: any;
  projection?: any;
  findOptions?: QueryOptions;
}

const defaultOptions: MongoosePaginateOptions = {
  lean: { parseId: true },
  page: 1,
  pageSize: 10,
  projection: {},
};

function paginate(query: FilterQuery<any>, options: MongoosePaginateOptions) {
  const model: Model<any> = this;
  options = {
    ...model.defaultPaginateOptions,
    ...options,
  };
  let { page, pageSize, lean, populate, projection, select, sort, findOptions } = options;

  // Prepare options
  page = page < 1 ? 1 : page;
  pageSize = pageSize < 1 ? 10 : pageSize;
  const limit = pageSize;
  const offset = limit * (page - 1);

  const countPromise = model.countDocuments(query).exec();
  const mQuery = model.find(query, projection, findOptions);

  if (populate) {
    mQuery.populate(populate);
  }

  mQuery.select(options.select);

  mQuery.select(select);
  mQuery.sort(sort);
  mQuery.lean(lean);

  mQuery.skip(offset);
  mQuery.limit(limit);

  const docsPromise = mQuery.exec();

  return Promise.all([
    // Get rows
    docsPromise,
    // Get count of all rows
    countPromise,
  ]).then((res) => {
    return {
      // Rows
      rows: res[0],
      // Total rows
      total: res[1],
      // Page
      page: page,
      // Page size
      pageSize: pageSize,
      // Total pages
      totalPages: Math.ceil(res[1] / pageSize),
    };
  });
}

function escapeReg(str: string) {
  return String(str).replace(/([.*+?=^!:${}()|[\]\/\\])/g, '\\$1');
}

export function convertPaginateQuery(query: ActionListQuery) {
  let mQuery: any = {};
  if (query?.search?.length > 0 && Array.isArray(query?.searchFields) && query?.searchFields?.length > 0) {
    mQuery['$or'] = query.searchFields.map((value) => {
      return { [value]: { $regex: '.*' + escapeReg(query.search) + '.*', $options: 'i' } };
    });
  }

  if (typeof query?.query === 'object') {
    // @ts-ignore
    if (query.query && query.query['$or']?.length > 0) {
      mQuery['$or'] = [
        // @ts-ignore
        ...query.query['$or'],
        ...mQuery['$or'],
      ];
    }
    return {
      ...query.query,
      ...mQuery,
    };
  } else {
    return query.query || mQuery;
  }
}

export function convertPaginateOptions(query: ActionListQuery, options: MongoosePaginateOptions) {
  const mOption = Object.assign({}, options);

  mOption.select = query?.fields ? query?.fields : options.select;
  mOption.sort = query?.sort ? query?.sort : options.sort;
  mOption.populate = query?.populate ? query?.populate : options.populate;
  mOption.lean = query?.lean ? query?.lean : options.lean;

  mOption.page = typeof query?.page !== 'undefined' ? query?.page : options.page;
  mOption.pageSize = typeof query?.pageSize !== 'undefined' ? query?.pageSize : options.pageSize;

  return mOption;
}

/**
 * This plugin purpose for paginate list with simple purpose
 * Ex:
 * Ref: https://github.com/aravindnc/mongoose-paginate-v2/blob/master/src/index.js
 * @param schema
 * @param options
 */
export default function applyPaginate(schema: Schema<any, any, any, any>, options?: MongoosePaginateOptions): void {
  // @ts-ignore
  schema.statics.defaultPaginateOptions = {
    ...defaultOptions,
    ...options,
  };
  schema.statics.paginate = paginate;
}
