'use strict';

// @ts-ignore
import mpath from 'mpath';
import { Document, Query, Schema } from 'mongoose';

export interface MongooseLeanDefaultsOptions {
  parseId?: boolean;
}

/**
 * This plugin purpose for convert object id to string when using lean feature
 * To enable plugin in lean. Need active with param "parseId"
 * Ex:
 * Ref: https://github.com/mongoosejs/mongoose-lean-getters/blob/master/index.js
 * @param schema
 * @param options
 */
export default function mongooseLeanId(
  schema: Schema<any, any, any, any>,
  options?: MongooseLeanDefaultsOptions,
): void {
  const fn = applyPluginMiddleware(schema, options);
  // Use `pre('find')` so this also works with `cursor()`
  // and `eachAsync()`, because those do not call `post('find')`
  schema.pre('find', function () {
    // @ts-ignore
    if (typeof this.map === 'function') {
      // @ts-ignore
      this.map((res) => {
        fn.call(this, res);
        return res;
      });
      // @ts-ignore
    } else if (typeof this.transform === 'function') {
      // @ts-ignore
      this.transform((res) => {
        fn.call(this, res);
        return res;
      });
    } else {
      // @ts-ignore
      this.options.transform = (res) => {
        fn.call(this, res);
        return res;
      };
    }
  });

  schema.post('findOne', fn);
  schema.post('findOneAndUpdate', fn);
}

function applyPluginMiddleware(schema: Schema, options?: MongooseLeanDefaultsOptions) {
  return function (this: Query<unknown, Document>, res: unknown) {
    applyPlugin.call(this, schema, res, options);
  };
}

function applyPlugin(
  this: Query<unknown, Document>,
  schema: Schema,
  res: unknown,
  options?: MongooseLeanDefaultsOptions,
  path?: string,
) {
  if (res == null) {
    return;
  }
  if (this._mongooseOptions.lean && this._mongooseOptions.lean.parseId) {
    if (Array.isArray(res)) {
      const len = res.length;
      for (let i = 0; i < len; ++i) {
        // @ts-ignore
        applyPluginToDoc.call(this, schema, res[i], this._fields, path);
      }
    } else {
      // @ts-ignore
      applyPluginToDoc.call(this, schema, res, this._fields, path);
    }

    for (let i = 0; i < schema.childSchemas.length; ++i) {
      const childPath = path ? path + '.' + schema.childSchemas[i].model.path : schema.childSchemas[i].model.path;
      const _schema = schema.childSchemas[i].schema;
      const doc = mpath.get(schema.childSchemas[i].model.path, res);
      if (doc == null) {
        continue;
      }
      applyPlugin.call(this, _schema, doc, childPath);
    }

    return res;
  } else {
    return res;
  }
}

function applyPluginToDoc(this: Query<unknown, Document>, schema: Schema, doc: unknown, fields: any, prefix?: string) {
  if (doc == null) {
    return;
  }
  if (Array.isArray(doc)) {
    for (let i = 0; i < doc.length; ++i) {
      applyPluginToDoc.call(this, schema, doc[i], fields, prefix);
    }
    return;
  }
  schema.eachPath((path, schematype) => {
    const pathWithPrefix = prefix ? prefix + '.' + path : path;
    if (this.selectedInclusively() && fields && fields[pathWithPrefix] == null) {
      return;
    }
    if (this.selectedExclusively() && fields && fields[pathWithPrefix] != null) {
      return;
    }
    if (schematype.instance === 'ObjectID') {
      // Apply for object id
      const value = mpath.get(path, doc);
      if (value) {
        // @ts-ignore
        mpath.set(path, value ? value.toString() : '', doc);
      }
      // mpath.set(path, schematype.applyGetters(mpath.get(path, doc), doc, true), doc);
    } else if (schematype.instance === 'Mixed') {
      // Apply for mixed object id
      const value = mpath.get(path, doc);
      if (value && value._bsontype === 'ObjectID') {
        if (value) {
          // @ts-ignore
          mpath.set(path, value ? value.toString() : '', doc);
        }
      }
    }
  });
}
