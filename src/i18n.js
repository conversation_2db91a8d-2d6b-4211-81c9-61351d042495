import { Theme } from "@fullcalendar/core/internal";
import { current } from "@reduxjs/toolkit";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { Popup } from "react-leaflet";
import { dark } from "react-syntax-highlighter/dist/esm/styles/hljs";

i18n.use(initReactI18next).init({
  lng: "Vn",
  fallbackLng: "En",
  interpolation: {
    escapeValue: false,
  },
  resources: {
    En: {
      translation: {
        crm: "CRM",
        report_message_in_month: "Report Message In Month",
        report_token_in_month: "Report Token In Month",
        max: "Max",
        min: "Min",
        mean: "Mean",
        dashboard: "Dashboard",
        templates: "Templates",
        Integrations: "Integrations",
        dataset_title: "Dataset",
        my_profile: "My Profile",
        order_user_menu:"Order User",
        list_bot_usage: "Overview Bot",
        report_message: "30-Day Message Reports",
        report_token: "30-Day Token Reports",
        name: "Name",
        copyright: "Copyright",
        desc: "Description",
        config: "Config",
        add: "Add",
        update: "Update",
        cancel: "Cancel",
        delete: "Delete",
        save: "Save",
        chat_test: "Chat Test Client",
        chat_history: "Chat History",
        publish: "Publish",
        workbot: "Workbot",
        snapshot: "Snapshot",
        dataset: "Dataset",
        knowledge: "Knowledge",
        qna: "QNA",
        vector_store: "Vector Store",
        knowledge_config: "Knowledge Config",
        qna_config: "QNA Config",
        vector_store_config: "Vector Store Config",
        vector_store_collection: "Vector Store Collection",
        vector_store_index: "Vector Store Index",
        user_list: "User List",
        username: "Username",
        accounts: "Accounts",
        profile: "Profile",
        free: "Free",
        auth: {
          send_recovery_email: "Send Recovery Email",
          login: "Login",
          logout: "Logout",
          register: "Register",
          forgot_password: "Forgot Your Password?",
          lock_screen: "Lock Screen",
          error: "Error",
          reset_password_with: "Reset Password With {{name}}",
          reset_password_description: "Enter your Email and instructions will be sent to you!",
          reset_password: "Reset Password",
          send_me_back: "Send me back",
          to_sign_in: "to the sign in",
          sign_in_to_your_account_to_start_using: "Sign in to your account to start using {{name}}",
          remember_me: "Keep me signed in",
          continue_with: "Or continue with",
          dont_have_an_account: "Don't have an account",
          sign_up: "Sign Up",
          create_an_account_to_start_using: "Create an account to start using {{name}}",
          create_account: "Create an Account",
          already_registered: "Already Registered?",
          accept_terms: "You accept our Terms and Conditions and Privacy Policy"
        },
        workbot: {
          list_bot: "List Bot",
          assign_package: "Assign Package",
          extend_package: "Extend Package",
          add_bot: "Add Bot",
          add_bot_success: "Add Bot Successfully",
          add_bot_fail: "Add Bot Fail",
          add_bot_error: "Add Bot Error",
          name: "Name",
          desc: "Description",
          assign_to: "Assign to",
          information: "Information",
          snapshot: "Snapshot",
          report: "Report",
          interaction: "Interaction",
          integration: "Integration",
          add: "Add",
          config: "Config",
          update: "Update",
          edit_flow: "Edit flow",
          cancel: "Cancel",
          delete: "Delete",
          save: "Save",
          chat_test: "Chat Test Client",
          chat_history: "Chat History",
          publish: "Publish",
          convert: "Convert",
          revert: "Revert",
          welcome_your_customer: "Welcome Your Customer",
          welcome_message: "Welcome message",
          message_placeholder: "Message Placeholder",
          appearance: "Appearance",
          chat_logo: "Chat Logo",
          theme: "Theme",
          theme_color: "Theme Color",
          powered_by_link: "Powered by link",
          chatbox_size: "Chatbox Size",
          width: "Width",
          height: "Height",
          enviroment: "Enviroment",
          development: "Development",
          production: "Production",
          position: "Position",
          align_to: "Align to",
          side_spacing: "Side spacing",
          bottom_spacing: "Bottom spacing",
          floating_chat_button_position: "Floating chat button position",
          popup_chatbox_position: "Popup chatbox position",
          update: "Update",
          light: "Light",
          dark: "Dark",
          left: "Left",
          right: "Right",
          filter_by_name_or_id: "Filter by name or id",
          type: "Type",
          flow: "Flow",
          chatbot: "Chatbot",
          add_flow: "Add Flow",
          add_chatbot: "Add Chatbot",
          suggestion: "Suggestions",
          suggestion_sentence_1: "Suggestion Sentence 1",
          suggestion_sentence_2: "Suggestion Sentence 2",
          suggestion_sentence_3: "Suggestion Sentence 3",
          enter_welcome_message: "Enter welcome message",
          enter_message_placeholder: "Enter message placeholder",
          assigned_bot: "AssignedBot",
          webhook: "Webhook",
          loading_webhook: "Loading webhook info...",
          enable_webhook: "Enable Webhook",
          copy_url: "Copy URL",
          webhook_slug_placeholder: "Webhook Slug",
          slug_validation_error: "Slug must be at least 8 characters and only contain lowercase letters, numbers, dash, or underscore.",
          production_link: "Production Link",
          test_link: "Test Link",
          webhook_updated: "Webhook updated successfully",
          webhook_update_failed: "Failed to update webhook",
          webhook_url_copied: "Webhook URL copied to clipboard",
          credit_amount: "Credit amount",
        },
        workgate: {
          messaging_event: "Messaging Event",
          add_integration: "Add Integration",
          add_faceBook_workgate: "Add Facebook Account",
          add_facebook_workgate_title: "Connect your Facebook account to Bizino",
          add_facebook_workgate_description: "Use Bizino to unlock purchase intent through messaging on Ads Manager, automatically optimize Facebook ads with CAPI",
          add_zalo_workgate: "Add Zalo Account",
          add_zalo_workgate_title: "Connect your Zalo account to Bizino",
          add_zalo_workgate_description: "Create connection and download installation + configuration file",
          continue_with_facebook: "Continue with Facebook",
          conect_zalo_account: "Create connection with Zalo",
          description: "Connection Name",
          download_ready_title: "Config file is ready to download",
          zalo_qr_setup: "Zalo QR Setup",
          choose_setup_mode: "Choose Setup Mode",
          create_new_workgate: "Create New Workgate",
          create_new_workgate_desc: "Create a new Zalo QR connection for a new account",
          relink_existing_workgate: "Re-link Existing Workgate",
          relink_existing_workgate_desc: "Connect a new Zalo account to an existing workgate",
          select_existing_workgate: "Select Existing Workgate",
          select_workgate_placeholder: "Choose a workgate to re-link...",
          no_existing_workgates: "No existing Zalo QR workgates found",
          clear_selection: "Clear Selection",
          relink_mode_info: "Re-link mode: This will update the selected workgate with new account data",
          continue: "Continue",
          back: "Back",
          zalo_qr_relink: "Zalo QR Re-link",
          relinking_to: "Re-linking to",
          zalo_relink: "Zalo QR Re-link",
          zalo_relink_successful: "Zalo QR re-linked successfully!",
          loading_workgates: "Loading workgates...",
          error_loading_workgates: "Error loading workgates",
          download_button: "Download",
          close_button: "Close",
          step1: "Step 1:",
          step1_desc: "Select BOT to connect with Zalo number.",
          step2: "Step 2:",
          step2_desc: "Download Json / App to your computer to run Zalo personal.",
          step3: "Step 3:",
          step3_desc: "Run the App and scan Zalo on the computer.",
          delete_integration: "Delete Integration",
          delete_integration_confirm: "Are you sure you want to delete this integration?",
          delete_integration_success: "Delete Integration Successfully",
          yes: "Yes",
          no: "No",
          confirm_delete: "Confirm",
          cancel: "Cancel",
          edit_title: "Edit Integration",
          name: "Name",
          other_setting: "Other Settings",
          status: "Status",
          download_files: "Download Files",
          zalo_setup_file: "Zalo Setup File",
          title: "Integration",
          edit: "Edit",
          type: "Type",
          page_name: "Page name",
          page_id: "Page ID",
          details: "Integration details",
          selectPost: "Select Post",
          zalo_auth_failed: "Zalo authentication failed",
          no_bot_available: "No bot available for integration",
          zalo_qr_integration: "Zalo QR Integration",
          zalo_integration_success: "Zalo integration created successfully!",
          zalo_integration_failed: "Failed to create Zalo integration!",
          zalo_qr_authentication: "Zalo QR Code Authentication",
          integration_created_successfully: "Integration Created Successfully",
          zalo_integration_complete: "Zalo Integration Complete!",
          zalo_integration_complete_desc: "Your Zalo account has been successfully connected and the integration has been created.",
          connected_account: "Connected Account",
          zalo_user: "Zalo User",
          integration_details: "Integration Details",
          id: "ID",
          zalo_qr_code: "Zalo QR Code",
          active: "Active",
          add_another: "Add Another",
          close: "Close",
          auth_token_not_found: "Authentication token not found. Please login again.",
          connecting_to_zalo_service: "Connecting to Zalo authentication service...",
          generating_qr_code: "Generating QR code...",
          scan_qr_with_zalo: "Scan the QR code with your Zalo app to login",
          error_generating_qr: "Error generating QR code",
          qr_scanned_by: "QR code scanned by",
          user: "User",
          login_successful: "Login successful!",
          zalo_auth_successful: "Zalo authentication successful!",
          error_processing_auth: "Error processing authentication",
          auth_error: "Authentication error",
          unknown_error: "Unknown error",
          auth_failed: "Authentication failed",
          connection_error_retry: "Connection error. Please try again.",
          connection_error: "Connection error",
          auth_successful: "Authentication Successful!",
          welcome: "Welcome",
          zalo_login_qr_code: "Zalo Login QR Code",
          open_zalo_scan_qr: "Open Zalo app and scan this QR code to login",
          click_generate_qr_start: "Click \"Generate QR Code\" to start authentication",
          zalo_authentication: "Zalo Authentication",
          ready: "Ready",
          connecting: "Connecting...",
          waiting_for_scan: "Waiting for scan",
          qr_scanned: "QR Scanned",
          authenticated: "Authenticated",
          error: "Error",
          generate_qr_code: "Generate QR Code",
          reset: "Reset",
          add_another_account: "Add Another Account",
          bot_required: "Bot selection is required",
          workgate_details: "WorkGate Detail",
          workgate_id: "ID WorkGate",
          goto_workgate_detail: "View Integration Details",
          credit_amount: "Credit amount",
          // Integration Detail specific translations
          integration_name_required: "Integration name is required",
          bot_uuid_required: "Bot UUID is required",
          status_required: "Status is required",
          general_info: "General Information",
          event_info: "Event Information",
          loading: "Loading...",
          not_found_title: "Workgate Not Found",
          not_found_description: "The workgate you are looking for does not exist or has been deleted.",
          back_to_list: "Back to List",
          error_loading_workgate: "Error loading workgate",
          please_select_bot: "Please select a bot",
          invalid_feed_event: "There are invalid feed events. Please check again!",
          invalid_data: "Invalid data",
          update_error: "An error occurred while updating",
          no_bot_selected: "No bot selected",
          click_to_open_bot_settings: "Click to open bot settings",
          open_bot_settings: "Open bot settings",
          status_active_desc: "Active: Normal operation (receive messages → AI processing → reply)",
          status_inactive_desc: "Inactive: Only save chat history (receive messages → save messages)",
          select_status: "Select status",
          enter_workgate_name: "Enter workgate name",
          inactive: "Inactive",
          disabled: "Disabled",
        },
        snapshot: {
          name: "Name",
          action: "Action",
          updated: "Updated",
          deleted: "Deleted",
          confirmed: "Confirmed",
          add: "Add Snapshot",
        },
        dataset: {
          title_knowledge: "About Knowledge",
          title_qna: "About QNA",
          title_vector_store: "About Vector Store",
          action: "Action",
          name: "Name",
          desc: "Description",
          type: "Type",
          add: "Add Dataset",
          config: "Config",
          update: "Update",
          cancel: "Cancel",
          delete: "Delete",
          edit: "Edit",
          create: "Create",
          creating: "Creating",
          save: "Add",
          detail: "Detail",
          vector_config: "Vector Config",
          fields: "Fields",
          vector_fields: "Vector Fields",
          parse_fields: "Parse Fields",
          index_fields: "Index Fields",
          clean_knowledges: "Clean Knowledges",
          clean_confirm: "Do you want Clean Knowledges?",
          clean_success: "Clean Successfully",
          delete_confirm: "Do you want delete Knowledge?",
          delete_success: "Delete Successfully",
          update_success: "Update Successfully",
          list_of: "List of",
          search_success: "Search Successfully",
          clean_search_result: "Clean Search Result",
          content: "Content",
          content_placeholder: "Knowledge content...",
        },
        user: {
          active: "Active",
          inactive: "Inactive",
          package_assigned_successfully: "Package Assigned Successfully",
          package_extended_successfully: "Package Extended Successfully",
          status_active: "Active",
          status_pending: "Pending",
          status_deleted: "Deleted",
        },
        chats: {
          about: "About",
          channel: "Channel",
          customer_information: "Customer Information",
          ai_chat: "AI Chat",
          manual_chat: "Manual Chat",
          search: "Search...",
        },
        faq: {
          question_1: "How does Bizino AI optimize the sales process?",
          content_1: "Bizino AI analyzes customer needs and provides suitable product recommendations, accelerating purchasing decisions. It optimizes sales steps, minimizing time and effort for the sales team.",
          question_2: "What technology platforms can Bizino AI support?",
          content_2: "Bizino AI can easily integrate with existing business technology platforms such as CRM, order management systems, and customer communication platforms (Zalo, Facebook, Email, etc.).",
          question_3: "How does Bizino AI help businesses save costs?",
          content_3: "Bizino AI reduces staffing and training costs by automating sales and customer service tasks. This lowers operational expenses and enhances team efficiency.",
          question_4: "Does Bizino AI support multiple languages?",
          content_4: "Yes, Bizino AI supports multiple languages, enabling businesses to serve international customers and expand their market reach without language barriers.",
          question_5: "Can Bizino AI operate 24/7?",
          content_5: "Yes, Bizino AI operates 24/7, always ready to assist and support customers anytime, ensuring continuous engagement even outside business hours.",
        },
        template: {
          list_template: "List template",
          add_template: "Add Template",
          name: "Name",
          desc: "Description",
          add: "Add",
          config: "Config",
          update: "Update",
          cancel: "Cancel",
          delete: "Delete",
          save: "Add",
          detail: "Detail",
          vector_config: "Vector Config",
          fields: "Fields",
          vector_fields: "Vector Fields",
          parse_fields: "Parse Fields",
          view_template: "View Template",
          edit_template: "Edit Template",
          create_template: "Create Template",
          delete_confirm: "You want to delete this template?",
          use_template: "Use template",
          clone_template: "Clone Template",
        },
        package: {
          list_package: "List Package",
          add_package: "Add Package",
          title: "Package",
          current_package: "Current Package",
          your_package: "Your Package",
          payment_cycle: "Payment Cycle",
          credit_limit: "Credit Limit",
          credit_used: "Credits Used",
          bot_limit: "Bot Limit",
          member_limit: "Member Limit",
          start_date: "Start Date",
          expiration_date: "Expiration Date",
          service_status: "Service Status",
          status_active: "Active",
          status_inactive: "Inactive",
          change_package: "Change",
          monthly: "30 Days",
          knowledge_limit: "Knowledge Limit",
          knowledge_record_limit: "Knowledge Record Limit",
          member_limit: "Member Limit",
          credit_limit: "Credit Limit",
          bot_setting_limit: "Bot Setting Limit",
          connector_limit: "Connector Limit",
          ccu_limit: "CCU Limit",
          contact_us: "Contact Us",
          month: "month",
          year: "year",
          current_plan: "Current Plan",
          select_plan: "Select",
          choose_plan: "Change your plan",
          change_anytime: "You can change the package anytime.",
          most_popular: "Most popular",
          credit_used: "Credit Used",
          description: "Description",
          price: "Price",
          price_month: "Price / month",
          price_year: "Price / year",
          name: "Package name",
          days: "Days",
          plane_duration: "Plane Duration",
          credit_warning: "You are approaching your credit limit. Buy more to avoid service interruption",
          buy_more_credits: "Buy more credits",
          credit_amount: "Credit amount",
        },
        chat: {
          name: "Name",
          phone: "Phone number",
          email: "Email",
          address: "Address",
          note: "Note",
          isRequired: "is required",
          enterNote: "Enter note",
          createContact: "Create Contact",
          quickCreate: "Quick",
          enterName: "Enter name",
          enterPhone: "Enter phone number",
          enterEmail: "Enter email",
          enterAddress: "Enter address",
          contactCreated: "Contact created",
          no_contact_found: "No contact found",
          updateContact: "Update",
          linkContact: "Link Contact",
          unlinkContact: "Unlink Contact",
          contactUpdated: "Contact updated",
          updateFailed: "Update failed",
          deleteContact: "Delete",
          deleteConfirm: "Are you sure you want to delete this contact?",
          contactDeleted: "Contact deleted",
          deleteFailed: "Delete failed",
        },
        order_user: {
          title: "Orders",
          created_at: "Created At",
          total: "Total",
          content: "Content",
          status: "Status",
          actions: "Actions",
          view: "View",
          page: "Page",
          of: "of",
          prev: "Prev",
          next: "Next",
          status_completed: "Completed",
          status_success: "Success",
          status_pending: "Pending",
          status_failed: "Failed",
          status_error: "Error",
          details: "Order Details",
          transaction_id: "Transaction ID",
          description: "Description",
          package_info: "Package Information",
          package_name: "Name",
          package_description: "Description",
          package_limits: "Package Limits",
          knowledge_limit: "Knowledge Limit",
          knowledge_record_limit: "Knowledge Record Limit",
          member_limit: "Member Limit",
          days: "Days",
          days_unit: "days",
          credit_limit: "Credit Limit",
          monthly_credit_limit: "Monthly Credit Limit",
          bot_setting_limit: "Bot Setting Limit",
          connector_limit: "Connector Limit",
          ccu_limit: "CCU Limit",
          status_active: "Active",
          status_inactive: "Inactive",
          loading_package: "Loading package details..."
        },
        checkout: {
          current_package: "Current Package",
          new_package: "New Package",
          package: "Package",
          order_info: "Order Information",
          total: "Total",
          continue: "Continue",
          payment_method: "Payment Method",
          qr_code: "QR Code Payment",
          bank_transfer: "Bank Transfer",
          invoice_request: "Invoice Request",
          export_invoice: "Export Invoice",
          invoice_type: "Invoice Type",
          personal: "Personal",
          company: "Company",
          company_name: "Company Name",
          company_name_placeholder: "Enter company name",
          personal_name: "Full Name",
          personal_name_placeholder: "Enter full name",
          tax_code: "Tax Code",
          tax_code_placeholder: "Enter tax code",
          personal_id: "ID/Passport",
          personal_id_placeholder: "Enter ID/Passport",
          address: "Address",
          address_placeholder: "Enter address",
          email: "Email",
          email_placeholder: "Enter email",
          phone: "Phone Number",
          phone_placeholder: "Enter phone number",
          knowledge_limit: "Knowledge Limit",
          knowledge: "knowledge",
          knowledge_record_limit: "Record Limit",
          knowledge_record: "record",
          member_limit: "Member Limit",
          member: "member",
          bot_setting_limit: "Bot Limit",
          bot: "bot",
          connector_limit: "Connector Limit",
          connector: "connector",
          ccu_limit: "CCU Limit",
          ccu: "CCU",
          start_date: "Start Date",
          expiration_date: "Expiration Date",
          credit_limit: "Credit Limit",
          credits_used: "Credits Used",
          currency: "₫",
          terms_prefix: "By paying, I accept the ",
          terms_of_service: "Terms of Service",
          privacy_policy: "Privacy Policy",
          and: "and",
          of_bizino: "of Bizino.",
          cannot_get_user_info: "Cannot get user info",
          cannot_get_package_info: "Cannot get package info",
          invoice_info_invalid: "Please check your invoice information",
          company_name_required: "Please enter company name",
          tax_code_required: "Please enter tax code",
          tax_code_invalid: "Tax code must be 10-13 digits",
          personal_name_required: "Please enter full name",
          personal_id_required: "Please enter ID/Passport",
          personal_id_invalid: "ID/Passport must be 9-12 digits",
          email_required: "Please enter email",
          email_invalid: "Invalid email",
          phone_required: "Please enter phone number",
          phone_invalid: "Phone number must start with 0 and have 10 digits",
          remaining_credits: "Remaining Credits",
          remaining_amount: "Remaining Amount",
          package_price: "Package Price",
          final_amount: "Final Amount",
          enter_coupon: "Enter coupon code",
          apply: "Apply",
          coupon_required: "Please enter a coupon code",
          coupon_invalid: "Invalid coupon code",
          coupon_success: "Coupon applied successfully",
          coupon_error: "Failed to apply coupon",
          credit_amount: "Credit amount",
        },
        billing: {
          payment: "Payment",
          pending_payment: "Pending Payment",
          payment_success: "Payment Successful",
          package_info: "Package Information",
          service_package: "Service Package",
          amount: "Amount",
          features: "Features",
          knowledge_limit: "Knowledge Limit",
          member_limit: "Member Limit",
          credit_limit: "Credit Limit",
          bot_limit: "Bot Limit",
          connector_limit: "Connector Limit",
          transfer_info: "Transfer Information",
          bank: "Bank",
          account_number: "Account Number",
          account_holder: "Account Holder",
          content: "Content",
          scan_qr: "Scan QR Code to Pay",
          scan_qr_instruction: "Scan the QR code using your banking app to make payment",
          redirect_notice: "After successful payment, you will be automatically redirected to the order list page",
          cannot_get_user_info: "Cannot get user information",
          cannot_get_package_info: "Cannot get package information",
          cannot_create_order: "Cannot create payment order! Please try again later",
          package_not_found: "Package information not found",
          try_again_later: "Please try again later",
          currency: "₫",
          credit_amount: "Credit amount",
        },
        used: "Used",
        limit: "Limit",
      }
    },
    Vn: {
      translation: {
        crm: "CRM",
        report_message_in_month: "Báo cáo tin nhắn trong tháng",
        report_token_in_month: "Báo cáo Token trong tháng",
        max: "Lớn nhất",
        min: "Nhỏ nhất",
        mean: "Trung bình",
        dashboard: "Trang Chủ",
        templates: "Mẫu",
        Integrations: "Tích Hợp",
        dataset_title: "Kho Kiến Thức",
        my_profile: "Trang Cá Nhân",
        order_user_menu: "Đơn hàng",
        list_bot_usage: "Tổng Quan Bot",
        report_message: "Thống kê tin nhắn (30 ngày)",
        report_token: "Thống kê token (30 ngày)",
        name: "Tên",
        copyright: "Bản quyền",
        desc: "Mô tả",
        config: "Cấu hình",
        add: "Thêm",
        update: "Cập nhật",
        cancel: "Hủy",
        delete: "Xóa",
        save: "Lưu",
        chat_test: "Chat Test Client",
        chat_history: "Chat History",
        publish: "Phát hành",
        workbot: "Workbot",
        snapshot: "Bản sao",
        Dataset: "Nguồn dữ liệu",
        Knowledge: "Kiến thức",
        qna: "Kho câu hỏi",
        knowledges: "Kiến thức",
        vectorstore: "Dữ liệu vector",
        Chat: "Tin nhắn",
        chat: "Tin nhắn",
        vector_store: "Vector Store",
        knowledge_config: "Knowledge Config",
        qna_config: "QNA Config",
        vector_store_config: "Vector Store Config",
        vector_store_collection: "Vector Store Collection",
        vector_store_index: "Vector Store Index",
        user_list: "Danh Sách người dùng",
        username: "Tài khoản",
        accounts: "Quản lý Tài khoản",
        profile: "Trang Cá Nhân",
        free: "Miễn phí",
        // For CRM Page
        messages: 'Tin nhắn',
        evalTokens: 'Token đầu vào',
        promptTokens: 'Token đầu ra',
        auth: {
          send_recovery_email: "Gửi email khôi phục",
          login: "Đăng nhập",
          logout: "Đăng xuất",
          register: "Đăng ký",
          forgot_password: "Bạn quên mật khẩu?",
          lock_screen: "Khoá màn hình",
          error: "Lỗi",
          reset_password_with: "Đặt lại mật khẩu cùng với {{name}}",
          reset_password_description: "Nhập Email và thông báo sẽ gửi đến cho bạn",
          reset_password: "Đặt lại mật khẩu",
          send_me_back: "Quay lại",
          to_sign_in: " Trang Đăng nhập",
          sign_in_to_your_account_to_start_using: "Đăng nhập với tài khoản để sử dụng {{name}}",
          remember_me: "Ghi nhớ tài khoản",
          continue_with: "Hoặc tiếp tục với",
          dont_have_an_account: "Chưa có tài khoản",
          sign_up: "Đăng ký",
          create_an_account_to_start_using: "Tạo tài khoản để sử dụng {{name}}",
          create_account: "Tạo tài khoản",
          already_registered: "Đã tạo tài khoản",
          accept_terms: "Bạn chấp nhận Điều khoản và Điều kiện và Chính sách Bảo mật của chúng tôi"
        },
        workbot: {
          list_bot: "Danh sách Bot",
          assign_package: "Kích hoạt gói",
          extend_package: "Gia hạn gói",
          add_bot: "Thêm Bot",
          add_bot_success: "Thêm Bot thành công",
          add_bot_fail: "Thêm Bot thất bại",
          add_bot_error: "Thêm Bot lỗi",
          assign_to: "Người phụ trách",
          information: "Thông tin",
          snapshot: "Sao Lưu",
          report: "Báo cáo",
          interaction: "Tương tác",
          integration: "Tích Hợp",
          name: "Tên",
          desc: "Mô tả",
          add: "Thêm",
          config: "Cấu hình",
          update: "Cập nhật",
          edit_flow: "Chỉnh sửa flow",
          cancel: "Huỷ",
          delete: "Xoá",
          edit: "Sửa",
          save: "Lưu",
          chat_test: "Mở chat",
          chat_history: "Lịch sử chat",
          publish: "Phát hành",
          convert: "Chuyển đổi",
          revert: "Khôi phục",
          welcome_your_customer: "Lời chào",
          welcome_message: "Tin nhắn chào mừng",
          message_placeholder: "Ô nhập tin nhắn",
          appearance: "Giao diện",
          chat_logo: "Logo Chat",
          theme: "Nền",
          theme_color: "Màu Nền",
          powered_by_link: "Link bổ sung",
          chatbox_size: "Kích thước Chatbox",
          width: "Chiều rộng",
          height: "Chiều cao",
          enviroment: "Môi trường",
          development: "Thử nghiệm",
          production: "Chính thức",
          position: "Vị trí",
          align_to: "Căn lề",
          side_spacing: "Khoảng cách bên",
          bottom_spacing: "Khoảng cách dưới",
          floating_chat_button_position: "Vị trí Công tắc Chat",
          popup_chatbox_position: "Vị trí Chatbox",
          update: "Cập Nhật",
          light: "Sáng",
          dark: "Tối",
          left: "Trái",
          right: "Phải",
          filter_by_name_or_id: "Chọn BOT: Lọc theo tên hoặc id",
          type: "Loại",
          flow: "Flow",
          add_flow: "Thêm Flow",
          add_chatbot: "Thêm Chatbot",
          suggestion: "Gợi ý",
          suggestion_sentence_1: "Câu gợi ý số 1",
          suggestion_sentence_2: "Câu gợi ý số 2",
          suggestion_sentence_3: "Câu gợi ý số 3",
          enter_welcome_message: "Nhập câu chào mừng",
          enter_message_placeholder: "Ô nhập tin nhắn",
          assigned_bot: "Bot đảm nhận",
          webhook: "Webhook",
          loading_webhook: "Đang tải thông tin webhook...",
          enable_webhook: "Bật Webhook",
          copy_url: "Sao chép URL",
          webhook_slug_placeholder: "Link Webhook",
          slug_validation_error: "Đường dẫn phải có ít nhất 8 ký tự và chỉ chứa chữ thường, số, dấu gạch ngang hoặc gạch dưới.",
          production_link: "Link Chính thức",
          test_link: "Link Thử nghiệm",
          webhook_updated: "Cập nhật webhook thành công",
          webhook_update_failed: "Cập nhật webhook thất bại",
          webhook_url_copied: "Đã sao chép URL webhook",
          credit_amount: "Số lượng credit",
        },
        workgate: {
          messaging_event: "Event nhắn tin",
          add_integration: "Thêm kết nối",
          add_faceBook_workgate: "Thêm tài khoản Facebook",
          add_facebook_workgate_title: "Kết nối tài khoản Facebook của bạn với Bizino",
          add_facebook_workgate_description: "Sử dụng Bizino để mở khoá mục tiêu mua hàng qua tin nhắn trên Ads Manager, tự động tối ưu quảng cáo Facebook với CAPI",
          continue_with_facebook: "Kết nối với Facebook",
          add_zalo_workgate: "Thêm tài khoản Zalo",
          add_zalo_workgate_title: "Kết nối tài khoản Zalo của bạn với Bizino",
          add_zalo_workgate_description: "Tạo kết nối và tải file cài đặt + cấu hình",
          conect_zalo_account: "Tạo kết nối Zalo",
          description: "Tên kết nối",
          download_ready_title: "Tải xuống file cấu hình",
          zalo_qr_setup: "Thiết lập Zalo QR",
          choose_setup_mode: "Chọn chế độ thiết lập",
          create_new_workgate: "Tạo Workgate mới",
          create_new_workgate_desc: "Tạo kết nối Zalo QR mới cho tài khoản mới",
          relink_existing_workgate: "Kết nối lại Workgate hiện có",
          relink_existing_workgate_desc: "Kết nối tài khoản Zalo mới với workgate đã có",
          select_existing_workgate: "Chọn Workgate hiện có",
          select_workgate_placeholder: "Chọn workgate để kết nối lại...",
          no_existing_workgates: "Không tìm thấy workgate Zalo QR nào",
          clear_selection: "Xóa lựa chọn",
          relink_mode_info: "Chế độ kết nối lại: Sẽ cập nhật workgate đã chọn với dữ liệu tài khoản mới",
          continue: "Tiếp tục",
          back: "Quay lại",
          zalo_qr_relink: "Kết nối lại Zalo QR",
          relinking_to: "Đang kết nối lại với",
          zalo_relink: "Kết nối lại Zalo QR",
          zalo_relink_successful: "Kết nối lại Zalo QR thành công!",
          loading_workgates: "Đang tải workgates...",
          error_loading_workgates: "Lỗi khi tải workgates",
          download_button: "Tải xuống",
          close_button: "Đóng",
          step1: "Bước 1:",
          step1_desc: "Chọn BOT kết nối với số Zalo.",
          step2: "Bước 2:",
          step2_desc: "Tải Json / App về máy tính sẽ chạy Zalo cá nhân.",
          step3: "Bước 3:",
          step3_desc: "Chạy App và quét Zalo trên máy tính.",
          delete_integration: "Xoá kết nối",
          delete_integration_confirm: "Bạn có chắc chắn muốn xoá kết nối này?",
          delete_integration_success: "Xoá kết nối thành công",
          confirm_delete: "Xác nhận",
          yes: "Có",
          no: "Không",
          cancel: "Hủy",
          edit_title: "Chỉnh sửa kết nối",
          name: "Tên",
          other_setting: "Cài đặt khác",
          status: "Trạng thái",
          download_files: "Các file tải xuống",
          zalo_setup_file: "File cài đặt Zalo",
          title: "Kết nối",
          edit: "Chỉnh sửa",
          type: "Loại",
          page_name: "Tên trang",
          page_id: "ID trang",
          details: "Thông tin",
          selectPost: "Chọn bài viết",
          zalo_auth_failed: "Xác thực Zalo thất bại",
          no_bot_available: "Không có bot nào khả dụng để tích hợp",
          zalo_qr_integration: "Tích hợp Zalo QR",
          zalo_integration_success: "Tạo tích hợp Zalo thành công!",
          zalo_integration_failed: "Tạo tích hợp Zalo thất bại!",
          zalo_qr_authentication: "Xác thực mã QR Zalo",
          integration_created_successfully: "Tạo tích hợp thành công",
          zalo_integration_complete: "Hoàn thành tích hợp Zalo!",
          zalo_integration_complete_desc: "Tài khoản Zalo của bạn đã được kết nối thành công và tích hợp đã được tạo.",
          connected_account: "Tài khoản đã kết nối",
          zalo_user: "Người dùng Zalo",
          integration_details: "Chi tiết tích hợp",
          id: "ID",
          zalo_qr_code: "Mã QR Zalo",
          active: "Hoạt động",
          add_another: "Thêm tài khoản khác",
          close: "Đóng",
          auth_token_not_found: "Không tìm thấy token xác thực. Vui lòng đăng nhập lại.",
          connecting_to_zalo_service: "Đang kết nối đến dịch vụ xác thực Zalo...",
          generating_qr_code: "Đang tạo mã QR...",
          scan_qr_with_zalo: "Quét mã QR bằng ứng dụng Zalo để đăng nhập",
          error_generating_qr: "Lỗi khi tạo mã QR",
          qr_scanned_by: "Mã QR đã được quét bởi",
          user: "Người dùng",
          login_successful: "Đăng nhập thành công!",
          zalo_auth_successful: "Xác thực Zalo thành công!",
          error_processing_auth: "Lỗi khi xử lý xác thực",
          auth_error: "Lỗi xác thực",
          unknown_error: "Lỗi không xác định",
          auth_failed: "Xác thực thất bại",
          connection_error_retry: "Lỗi kết nối. Vui lòng thử lại.",
          connection_error: "Lỗi kết nối",
          auth_successful: "Xác thực thành công!",
          welcome: "Chào mừng",
          zalo_login_qr_code: "Mã QR đăng nhập Zalo",
          open_zalo_scan_qr: "Mở ứng dụng Zalo và quét mã QR này để đăng nhập",
          click_generate_qr_start: "Nhấp \"Tạo mã QR\" để bắt đầu xác thực",
          zalo_authentication: "Kết nối Zalo QR",
          ready: "Bắt đầu",
          connecting: "Đang kết nối...",
          waiting_for_scan: "Đang chờ quét",
          qr_scanned: "Đã quét QR",
          authenticated: "Đã xác thực",
          error: "Lỗi",
          generate_qr_code: "Tạo mã QR",
          reset: "Đặt lại",
          add_another_account: "Thêm tài khoản khác",
          bot_required: "Bắt buộc chọn bot",
          workgate_details: "Chi tiết WorkGate",
          workgate_id: "ID WorkGate",
          goto_workgate_detail: "Xem chi tiết tích hợp",
          credit_amount: "Credit amount",
        },
        snapshot: {
          list_snapshot: "Danh sách sao lưu",
          name: "Tên",
          action: "Thao tác",
          updated: "Cập nhật",
          deleted: "Xoá",
          deleted: "Xoá",
          confirmed: "Xác Nhận",
          add: "Thêm Sao Lưu",
        },
        dataset: {
          title_knowledge: "Giới thiệu Knowledge",
          title_qna: "Giới thiệu QNA",
          title_vector_store: "Giới thiệu Vector Store",
          action: "Thao tác",
          name: "Tên",
          desc: "Mô tả",
          type: "Loại",
          add: "Thêm dữ liệu",
          config: "Cấu hình",
          update: "Cập nhật",
          cancel: "Huỷ",
          delete: "Xoá",
          edit: "Sửa",
          create: "Tạo",
          creating: "Đang tạo",
          save: "Lưu",
          detail: "Chi tiết",
          vector_config: "Cấu hình Vector",
          fields: "Fields",
          vector_fields: "Vector Fields",
          parse_fields: "Parse Fields",
          index_fields: "Index Fields",
          clean_knowledges: "Xoá kiến thức",
          clean_confirm: "Bạn muốn xoá tất cả?",
          delete_confirm: "Bạn muốn xoá kiến thức này?",
          clean_success: "Xoá tất cả kiến thức thành công",
          delete_success: "Xoá Knowledge thành công",
          update_success: "Cập nhật thành công",
          list_of: "Danh sách",
          search_success: "Tìm kiếm thành công",
          clean_search_result: "Xoá Tìm kiếm",
          content: "Nội dung",
          content_placeholder: "Nội dung kiến thức",
        },
        user: {
          active: "Kích hoạt",
          inactive: "Khoá kích hoạt",
          package_assigned_successfully: "Kích hoạt gói thành công",
          package_extended_successfully: "Gia hạn gói thành công",
          status_active: "Kích hoạt",
          status_pending: "Chờ xử lý",
          status_deleted: "Đã xoá",
        },
        chats: {
          about: "Giới thiệu",
          channel: "Kênh",
          customer_information: "Thông tin khách hàng",
          ai_chat: "AI Chat",
          manual_chat: "Chat thủ công",
          search: "Tìm kiếm...",
        },
        faq: {
          question_1: "Bizino AI giúp tối ưu hóa quy trình bán hàng như thế nào?",
          content_1: "Bizino AI phân tích nhu cầu của khách hàng và đưa ra những gợi ý sản phẩm phù hợp, từ đó thúc đẩy quyết định mua hàng nhanh chóng. Nó giúp tối ưu hóa các bước bán hàng, giảm thiểu thời gian và công sức của đội ngũ nhân viên.",
          question_2: "Bizino AI có thể hỗ trợ những nền tảng công nghệ nào?",
          content_2: "Bizino AI có khả năng tích hợp dễ dàng với các nền tảng công nghệ hiện có của doanh nghiệp như CRM, hệ thống quản lý đơn hàng, và các nền tảng giao tiếp khách hàng (Zalo, Facebook, Email…).",
          question_3: "Làm thế nào Bizino AI giúp tiết kiệm chi phí cho doanh nghiệp?",
          content_3: "Bizino AI giúp tiết kiệm chi phí nhân sự và đào tạo bằng cách tự động hóa các tác vụ bán hàng và chăm sóc khách hàng. Điều này giúp doanh nghiệp giảm chi phí vận hành và nâng cao hiệu quả công việc của đội ngũ.",
          question_4: "Bizino AI có hỗ trợ nhiều ngôn ngữ không?",
          content_4: "Bizino AI hỗ trợ đa ngôn ngữ, giúp doanh nghiệp phục vụ khách hàng quốc tế và mở rộng phạm vi kinh doanh mà không bị giới hạn bởi ngôn ngữ.",
          question_5: "Bizino AI có thể hỗ trợ 24/7 không?",
          content_5: "Đúng vậy, Bizino AI có thể hoạt động 24/7, luôn sẵn sàng tư vấn và hỗ trợ khách hàng bất cứ lúc nào, giúp doanh nghiệp duy trì kết nối và tương tác với khách hàng ngay cả ngoài giờ làm việc.",
        },
        template: {
          list_template: "Danh sách Template",
          add_template: "Thêm Template",
          name: "Tên",
          desc: "Mô tả",
          add: "Thêm",
          config: "Cấu hình",
          update: "Cập nhật",
          cancel: "Huỷ",
          delete: "Xoá",
          save: "Lưu",
          detail: "Chi tiết",
          vector_config: "Cấu hình Vector",
          fields: "Fields",
          vector_fields: "Vector Fields",
          parse_fields: "Parse Fields",
          view_template: "Xem Template",
          edit_template: "Sửa Template",
          create_template: "Tạo Template",
          delete_confirm: "Bạn muốn xoá Template này?",
          use_template: "Sử dụng",
          clone_template: "Sao chép Template",
        },
        package: {
          list_package: "Danh sách gói",
          add_package: "Thêm gói",
          title: "Gói dịch vụ",
          current_package: "Gói đang sử dụng",
          your_package: "Gói của bạn",
          payment_cycle: "Chu kỳ thanh toán",
          credit_limit: "Hạn mức credit",
          credit_used: "Credit đã dùng",
          bot_limit: "Giới hạn bot",
          member_limit: "Giới hạn thành viên",
          start_date: "Ngày bắt đầu",
          expiration_date: "Ngày kết hạn",
          service_status: "Trạng thái dịch vụ",
          status_active: "Đang hoạt động",
          status_inactive: "Không hoạt động",
          change_package: "Đổi gói",
          monthly: "30 Ngày",
          knowledge_limit: "Giới hạn kiến thức",
          knowledge_record_limit: "Giới hạn bản ghi kiến thức",
          member_limit: "Giới hạn thành viên",
          credit_limit: "Giới hạn credit",
          bot_setting_limit: "Giới hạn cài đặt bot",
          connector_limit: "Giới hạn kết nối",
          ccu_limit: "Giới hạn CCU",
          contact_us: "Liên hệ",
          month: "tháng",
          year: "năm",
          current_plan: "Đang sử dụng",
          select_plan: "Chọn gói",
          choose_plan: "Đổi gói phù hợp với bạn",
          change_anytime: "Bạn có thể thay đổi hoặc hủy gói bất kỳ lúc nào",
          most_popular: "PHỔ BIẾN NHẤT",
          credit_used: "Credit đã dùng",
          days: "ngày",
          description: "Mô tả",
          price: "Giá",
          price_month: "Giá / tháng",
          price_year: "Giá / năm",
          name: "Tên gói",
          plane_duration: "Thời gian sử dụng",
          credit_warning: "Bạn đã sử dụng gần tới giới hạn credit, hãy mua thêm để tránh gián đoạn khi sử dụng",
          buy_more_credits: "Mua thêm credit",
          credit_amount: "Số lượng credit",
        },
        chat: {
          name: "Tên",
          phone: "Số điện thoại",
          email: "Email",
          address: "Địa chỉ",
          note: "Ghi chú",
          isRequired: "là bắt buộc",
          enterNote: "Nhập ghi chú",
          createContact: "Tạo liên hệ",
          quickCreate: "Tạo nhanh",
          enterName: "Nhập tên",
          enterPhone: "Nhập số điện thoại",
          enterEmail: "Nhập email",
          enterAddress: "Nhập địa chỉ",
          contactCreated: "Liên hệ đã được tạo",
          no_contact_found: "Không tìm thấy liên hệ nào",
          updateContact: "Cập nhật",
          linkContact: "Liên kết liên hệ",
          unLinkContact: "Hủy liên kết",
          contactUpdated: "Liên hệ đã được cập nhật",
          updateFailed: "Cập nhật không thành công",
          deleteContact: "Xoá liên hệ",
          deleteConfirm: "Bạn có chắc chắn muốn xoá liên hệ này?",
          contactDeleted: "Liên hệ đã được xoá",
          deleteFailed: "Xoá không thành công",
        },
        order_user: {
          title: "Đơn hàng",
          created_at: "Ngày tạo",
          total: "Tổng tiền",
          content: "Nội dung",
          status: "Trạng thái",
          actions: "Thao tác",
          view: "Xem",
          page: "Trang",
          of: "trên",
          prev: "Trước",
          next: "Tiếp",
          status_completed: "Hoàn thành",
          status_success: "Thành công",
          status_pending: "Đang xử lý",
          status_failed: "Thất bại",
          status_error: "Lỗi",
          details: "Chi tiết đơn hàng",
          transaction_id: "Mã giao dịch",
          description: "Mô tả",
          package_info: "Thông tin gói",
          package_name: "Tên gói",
          package_description: "Mô tả",
          package_limits: "Giới hạn gói",
          knowledge_limit: "Giới hạn kiến thức",
          knowledge_record_limit: "Giới hạn bản ghi kiến thức",
          member_limit: "Giới hạn thành viên",
          days: "Số ngày",
          days_unit: "ngày",
          credit_limit: "Giới hạn credit",
          monthly_credit_limit: "Giới hạn credit hàng tháng",
          bot_setting_limit: "Giới hạn cài đặt bot",
          connector_limit: "Giới hạn kết nối",
          ccu_limit: "Giới hạn CCU",
          status_active: "Đang hoạt động",
          status_inactive: "Không hoạt động",
          loading_package: "Đang tải thông tin gói..."
        },
        checkout: {
          current_package: "Gói hiện tại",
          new_package: "Gói mới",
          package: "Gói dịch vụ",
          order_info: "Thông tin đơn hàng",
          total: "Tổng cộng",
          continue: "Tiếp tục",
          payment_method: "Hình thức thanh toán",
          qr_code: "Quét QR Code",
          bank_transfer: "Chuyển khoản ngân hàng",
          invoice_request: "Yêu cầu xuất hóa đơn",
          export_invoice: "Xuất hóa đơn",
          invoice_type: "Loại hóa đơn",
          personal: "Cá nhân",
          company: "Doanh nghiệp",
          company_name: "Tên công ty",
          company_name_placeholder: "Nhập tên công ty",
          personal_name: "Họ tên",
          personal_name_placeholder: "Nhập họ tên",
          tax_code: "Mã số thuế",
          tax_code_placeholder: "Nhập mã số thuế",
          personal_id: "Số CMND/CCCD",
          personal_id_placeholder: "Nhập số CMND/CCCD",
          address: "Địa chỉ",
          address_placeholder: "Nhập địa chỉ",
          email: "Email",
          email_placeholder: "Nhập email",
          phone: "Số điện thoại",
          phone_placeholder: "Nhập số điện thoại",
          knowledge_limit: "Giới hạn knowledge",
          knowledge: "knowledge",
          knowledge_record_limit: "Giới hạn bản ghi",
          knowledge_record: "bản ghi",
          member_limit: "Giới hạn thành viên",
          member: "thành viên",
          bot_setting_limit: "Giới hạn bot",
          bot: "bot",
          connector_limit: "Giới hạn connector",
          connector: "connector",
          ccu_limit: "Giới hạn CCU",
          ccu: "CCU",
          start_date: "Ngày bắt đầu",
          expiration_date: "Ngày kết hạn",
          credit_limit: "Credit giới hạn",
          credits_used: "Credit đã dùng",
          currency: "đ",
          terms_prefix: "Bằng việc thanh toán, tôi chấp nhận ",
          terms_of_service: "Điều khoản sử dụng dịch vụ",
          privacy_policy: "Chính sách bảo mật",
          and: "và",
          of_bizino: "của Bizino.",
          cannot_get_user_info: "Không thể lấy thông tin người dùng",
          cannot_get_package_info: "Không thể lấy thông tin gói dịch vụ",
          invoice_info_invalid: "Vui lòng kiểm tra lại thông tin hóa đơn",
          company_name_required: "Vui lòng nhập tên công ty",
          tax_code_required: "Vui lòng nhập mã số thuế",
          tax_code_invalid: "Mã số thuế phải có 10-13 số",
          personal_name_required: "Vui lòng nhập họ tên",
          personal_id_required: "Vui lòng nhập số CMND/CCCD",
          personal_id_invalid: "Số CMND/CCCD phải có 9-12 số",
          email_required: "Vui lòng nhập email",
          email_invalid: "Email không hợp lệ",
          phone_required: "Vui lòng nhập số điện thoại",
          phone_invalid: "Số điện thoại phải bắt đầu bằng số 0 và có 10 số",
          remaining_credits: "Credit còn lại",
          remaining_amount: "Số tiền còn lại",
          package_price: "Giá gói",
          final_amount: "Số tiền thanh toán",
          enter_coupon: "Nhập mã giảm giá",
          apply: "Áp dụng",
          coupon_required: "Vui lòng nhập mã giảm giá",
          coupon_invalid: "Mã giảm giá không hợp lệ",
          coupon_success: "Áp dụng mã giảm giá thành công",
          coupon_error: "Không thể áp dụng mã giảm giá",
          credit_amount: "Số lượng credit",
        },
        billing: {
          payment: "Thanh toán",
          pending_payment: "Đang chờ thanh toán",
          payment_success: "Thanh toán thành công",
          package_info: "Thông tin gói",
          service_package: "Gói dịch vụ",
          amount: "Số tiền",
          features: "Tính năng",
          knowledge_limit: "Giới hạn knowledge",
          member_limit: "Giới hạn thành viên",
          credit_limit: "Giới hạn credit",
          bot_limit: "Giới hạn bot",
          connector_limit: "Giới hạn connector",
          transfer_info: "Thông tin chuyển khoản",
          bank: "Ngân hàng",
          account_number: "Số tài khoản",
          account_holder: "Chủ tài khoản",
          content: "Nội dung",
          scan_qr: "Quét mã QR để thanh toán",
          scan_qr_instruction: "Quét mã QR bằng ứng dụng ngân hàng của bạn để thanh toán",
          redirect_notice: "Sau khi thanh toán thành công, hệ thống sẽ tự động chuyển hướng bạn đến trang danh sách đơn hàng",
          cannot_get_user_info: "Không thể lấy thông tin người dùng",
          cannot_get_package_info: "Không thể lấy thông tin gói",
          cannot_create_order: "Không thể tạo đơn thanh toán! Vui lòng thử lại sau",
          package_not_found: "Không tìm thấy thông tin gói",
          try_again_later: "Vui lòng thử lại sau",
          currency: "đ",
          credit_amount: "Credit amount",
        },
        used: "Đã sử dụng",
        limit: "Giới hạn",
      }
    },
  },
});

export default i18n;
