import React from 'react';
import { Link } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import { useTranslation } from "react-i18next";
import { useNavigate } from 'react-router-dom';
import { useSelector } from "react-redux";
import { motion } from 'framer-motion';

const CurrentPackage = ({ currentPackage }) => {
    const { t } = useTranslation();
    const { user } = useSelector((state) => state.auth);
    const navigate = useNavigate();

    const handleClick = () => {
        navigate('/package');
    };

    const formatDate = (date) => {
        if (!date) return 'N/A';
        return new Date(date).toLocaleDateString('vi-VN');
    };

    const packageDetails = [
        // {
        //     label: 'package.your_package',
        //     value: currentPackage?.packageName || 'N/A',
        //     showChangeButton: !user.teamId
        // },
        {
            label: 'package.payment_cycle',
            value: t('package.monthly')
        },
        {
            label: 'package.knowledge_limit',
            value: currentPackage?.knowledgeLimit || 0
        },
        {
            label: 'package.knowledge_record_limit',
            value: currentPackage?.knowledgeRecordLimit || 0
        },
        {
            label: 'package.credit_limit',
            value: currentPackage?.creditLimit?.toLocaleString('vi-VN') || 0,
            // showIcon: true
        },
        {
            label: 'package.credit_used',
            value: currentPackage?.currentUsage?.creditsUsed?.toLocaleString('vi-VN') || 0
        },
        {
            label: 'package.bot_setting_limit',
            value: currentPackage?.botSettingLimit || 0
        },
        {
            label: 'package.connector_limit',
            value: currentPackage?.connectorLimit || 0
        },
        {
            label: 'package.ccu_limit',
            value: currentPackage?.ccuLimit || 0
        },
        {
            label: 'package.member_limit',
            value: currentPackage?.memberLimit || 0
        },
        {
            label: 'package.start_date',
            value: formatDate(currentPackage?.startDate)
        },
        {
            label: 'package.expiration_date',
            value: formatDate(currentPackage?.expirationDate)
        },
        {
            label: 'package.service_status',
            value: currentPackage?.status === 1 ? t('package.status_active') : t('package.status_inactive'),
            isStatus: true
        }
    ];

    // Icon mapping cho từng dòng
    const iconMap = {
        'package.your_package': 'heroicons:star',
        'package.payment_cycle': 'heroicons:calendar',
        'package.knowledge_limit': 'heroicons:book-open',
        'package.knowledge_record_limit': 'heroicons:document-text',
        'package.credit_limit': 'heroicons:banknotes',
        'package.credit_used': 'heroicons:chart-bar',
        'package.bot_setting_limit': 'heroicons:cog-6-tooth',
        'package.connector_limit': 'heroicons:link',
        'package.ccu_limit': 'heroicons:user-group',
        'package.member_limit': 'heroicons:user',
        'package.start_date': 'heroicons:calendar-days',
        'package.expiration_date': 'heroicons:calendar-days',
        'package.service_status': 'heroicons:signal',
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white dark:bg-slate-800 rounded-2xl p-8 shadow-xl"
        >
            <h5 className="font-bold text-lg text-slate-900 dark:text-slate-200 mb-6 flex items-center gap-2">
                <Icon icon="heroicons:gift" className="w-7 h-7 mr-4 text-blue-400" />
                {t('package.your_package')}: {currentPackage?.packageName}
            </h5>
             <motion.button
               whileHover={{ scale: 1.05 }}
               whileTap={{ scale: 0.96 }}
               onClick={handleClick}
               className="ml-3 mb-6 w-full px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-800 text-white font-bold rounded-full shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400">
               {t('package.change_package')}
             </motion.button>
            <div className="space-y-4">
                <div className="space-y-3">
                    {packageDetails.map((detail, index) => (
                        <motion.div
                            key={index}
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.05 * index, duration: 0.4 }}
                            className="flex justify-between items-center group"
                        >
                            <span className="flex items-center gap-2 text-slate-500 dark:text-slate-400">
                                <Icon icon={iconMap[detail.label]} className="w-5 h-5 text-blue-300 group-hover:text-blue-500 transition-colors" />
                                {t(detail.label)}
                            </span>
                            <div className="flex items-center">
                                {detail.isStatus ? (
                                    <span className={`px-2 py-1 ${detail.value === t('package.status_active') ? 'bg-[#4CBF73]' : 'bg-red-500'} text-white rounded-full text-xs font-semibold`}>
                                        {detail.value}
                                    </span>
                                ) : (
                                    <>
                                        <span className="text-slate-900 dark:text-slate-200 font-semibold">
                                            {detail.value}
                                        </span>
                                        {detail.showIcon && (
                                            <Icon
                                                icon="heroicons:question-mark-circle"
                                                className="ml-1 text-slate-400"
                                            />
                                        )}
                                        
                                    </>
                                )}
                            </div>
                        </motion.div>
                    ))}
                </div>
            </div>
        </motion.div>
    );
}

export default CurrentPackage;