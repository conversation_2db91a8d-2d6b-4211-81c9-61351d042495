import React, { lazy, Suspense } from "react";
import { Routes, Route, Navigate } from "react-router-dom";

// home pages  & dashboard
//import Dashboard from "./pages/dashboard";
const Dashboard = lazy(() => import("./pages/dashboard/crm"));

const Login = lazy(() => import("./pages/auth/login"));
const Register = lazy(() => import("./pages/auth/register"));
const ForgotPass = lazy(() => import("./pages/auth/forgot-password"));
const LockScreen = lazy(() => import("./pages/auth/lock-screen"));
const Error = lazy(() => import("./pages/404"));
import Layout from "./layout/Layout";
import AuthLayout from "./layout/AuthLayout";
import ProtectedAdminRoute from "./components/ProtectAdminRouter";

const PricingPage = lazy(() => import("./pages/utility/pricing"));
const ComingSoonPage = lazy(() => import("./pages/utility/coming-soon"));
const UnderConstructionPage = lazy(() =>
  import("./pages/utility/under-construction")
);
const FaqPage = lazy(() => import("./pages/utility/faq"));
const Profile = lazy(() => import("./pages/utility/profile"));
const OrderUser = lazy(() => import("./pages/app/users/OrderUser"));
const ChangelogPage = lazy(() => import("./pages/changelog"));
const ChatPage = lazy(() => import("./pages/app/chat"));
const ProjectPostPage = lazy(() => import("./pages/app/projects"));
const ProjectDetailsPage = lazy(() =>
  import("./pages/app/projects/project-details")
);
const KnowledgePage = lazy(() => import("./pages/app/knowledge"));
const KnowledgeDetailPage = lazy(() => import("./pages/app/knowledge/knowledge-details"));
const QnaPage = lazy(() => import("./pages/app/qna"));
const QnaDetailPage = lazy(() => import("./pages/app/qna/qna-details"));
const VectorDBPage = lazy(() => import("./pages/app/vectordb"));
const VectorDBDetailPage = lazy(() => import("./pages/app/vectordb/vectordb-details"));
const UserListPage = lazy(() => import("./pages/app/users"));
const UserDetailsPage = lazy(() => import("./pages/app/users/user-details"));

const IntegrationsPage = lazy(() => import("./pages/app/integrations/index"));
const IntegrationDetail = lazy(() => import("./pages/app/integrations/IntegrationDetail"));
const TemplatePage = lazy(() => import("./pages/app/template/index"));
const TemplateDetail = lazy(() => import("./pages/app/template/templateDetail"));
const ChangePackage = lazy(() => import("./pages/app/package/ChangePackage"));
const BillingCycle = lazy(() => import("./pages/app/package/BillingCycle"))
const Checkout = lazy(() => import("./pages/app/package/CheckOut"))
const CreditCheckout = lazy(() => import("./pages/app/package/CreditCheckOut"))

import Loading from "@/components/Loading";
function App() {
  return (
    <main className="App  relative">
      <Routes>
        <Route path="/" element={<AuthLayout />}>
          <Route path="/" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/forgot-password" element={<ForgotPass />} />
          <Route path="/lock-screen" element={<LockScreen />} />
        </Route>
        <Route path="/*" element={<Layout />}>
          <Route path="dashboard" element={<Dashboard />} />
          <Route path="users" element={<UserListPage />} />
          <Route path="users/:id" element={<UserDetailsPage />} />
          <Route path="chat" element={<ChatPage />} />
          <Route path="knowledges" element={<KnowledgePage dataType={1} typeName="Knowledge" />} />
          <Route path="knowledges/:id" element={<KnowledgeDetailPage />} />
          <Route path="qna" element={<KnowledgePage dataType={2} typeName="Qna" />} />
          <Route path="qna/:id" element={<QnaDetailPage />} />
          <Route path="vectorstore" element={<KnowledgePage dataType={3} typeName="Vectorstore" />} />
          <Route path="vectorstore/:id" element={<VectorDBDetailPage />} />
          <Route path="workbots" element={
            // <ProtectedAdminRoute>
            <ProjectPostPage />
            // </ProtectedAdminRoute>
          } />
          <Route path={"workbots/:id"} element={<ProjectDetailsPage />} />
          <Route path={"workbots/:id/chat"} element={<ChatPage />} />

          {/* <Route path="workbots/:id/chat" element={<ChatLayout />}>
            <Route index element={<Blank />} />
            <Route path=":environment/:channel_id/:thread_id" element={<ChatContent />} />
          </Route> */}

          {/* <Route path={"workbots/:id/chat/:environment/:channel_id/:thread_id/:message_id"} element={<ChatPage />} /> */}
          <Route path={"workbots/:id/chat/:environment/:channel_id/:thread_id/:message_id?"} element={<ChatPage />} />
          <Route path="qna/:id" element={<QnaDetailPage />} />
          <Route path="vectorstore/:id" element={<VectorDBDetailPage />} />
          {/* <Route path="package" element={<PricingPage />} /> */}
          <Route path="package" element={<ChangePackage />} />
          <Route path="package/checkout" element={<Checkout />} />
          <Route path="package/credit-checkout" element={<CreditCheckout />} />
          <Route path="package/billing" element={<BillingCycle />} />
          <Route path="faq" element={<FaqPage />} />
          <Route path="profile" element={<Profile />} />
          <Route path="order-user" element={<OrderUser />} />
          {/* <Route path="package/change" element={<ChangePackage />} /> */}
          <Route path="changelog" element={<ChangelogPage />} />
          <Route path="*" element={
            <Suspense fallback={<Loading />}>
              <Error />
            </Suspense>
          } />

          <Route path="integration" element={<IntegrationsPage />} />
          <Route path="integration/:id" element={<IntegrationDetail />} />
          <Route path="template" element={<TemplatePage />} />
          <Route path="template/:id" element={<TemplateDetail />} />
        </Route>
        <Route
          path="/404"
          element={
            <Suspense fallback={<Loading />}>
              <Error />
            </Suspense>
          }
        />
        <Route
          path="/coming-soon"
          element={
            <Suspense fallback={<Loading />}>
              <ComingSoonPage />
            </Suspense>
          }
        />
        <Route
          path="/under-construction"
          element={
            <Suspense fallback={<Loading />}>
              <UnderConstructionPage />
            </Suspense>
          }
        />
      </Routes>
    </main>
  );
}

export default App;
