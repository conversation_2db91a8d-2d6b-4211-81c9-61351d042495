import React from "react";
import { Link } from "react-router-dom";
import Icon from "@/components/ui/Icon";
import Card from "@/components/ui/Card";
import BasicArea from "../chart/appex-chart/BasicArea";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { useCurrentQuery, useListQuery } from "@/store/api/teamPackage/teamPackageApiSlice";
import { motion } from 'framer-motion';
import { useNavigate } from "react-router-dom";

// import images
import ProfileImage from "@/assets/images/logo/ai_bot.svg";
import CurrentPackage from "@/components/ui/CurrentPackage";

const profile = () => {
  const { user, roleName } = useSelector((state) => state.auth);
  const authState = useSelector((state) => state.auth);
  const { t } = useTranslation();
  const { data: currentPackage } = useCurrentQuery();
  const navigate = useNavigate();

  const handleBuyMoreCredits = () => {
    navigate('/package/credit-checkout');
  }

  return (
    <div>
      <div className="space-y-5 profile-page">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="profiel-wrap px-4 sm:px-6 lg:px-8 pb-6 pt-6 rounded-2xl bg-white dark:bg-slate-800 relative z-[1] shadow-xl"
          style={{ background: 'linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%)' }}
        >
          {/* Background header */}
          <div className="bg-slate-900 dark:bg-slate-700 absolute left-0 top-0 h-52 md:h-32 w-full z-[-1] rounded-t-2xl"></div>

          {/* Profile content */}
          <div className="profile-box w-full">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
              {/* Avatar section */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="flex-none"
              >
                <div className="h-24 w-24 sm:h-32 sm:w-32 rounded-full ring-4 ring-slate-100 relative bg-white shadow-lg">
                  <img
                    src={ProfileImage}
                    alt=""
                    className="w-full h-full object-cover rounded-full"
                  />
                  <Link
                    to="#"
                    className="absolute right-0 bottom-0 h-8 w-8 bg-slate-50 text-slate-600 rounded-full shadow-sm flex flex-col items-center justify-center hover:bg-blue-100 transition-colors"
                  >
                    <Icon icon="heroicons:pencil-square" />
                  </Link>
                </div>
              </motion.div>

              {/* Info section */}
              <motion.div
                initial={{ opacity: 0, x: 40 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
                className="flex-1 w-full"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Name and Role */}
                  <div className="flex flex-col items-center md:items-start">
                    <div className="text-xl sm:text-2xl font-medium text-slate-900 dark:text-slate-200 mb-1">
                      {user.name}
                    </div>
                    <div className="text-sm font-light text-slate-400">
                      {roleName}
                    </div>
                  </div>

                  {/* Contact Info */}
                  <div className="w-full">
                    <ul className="space-y-4">
                      <li className="flex items-start space-x-3 rtl:space-x-reverse">
                        <div className="flex-none text-xl text-slate-950 dark:text-slate-300 md:text-slate-300">
                          <Icon icon="heroicons:envelope" />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-slate-950 dark:text-slate-300 md:text-slate-300 uppercase">
                            EMAIL
                          </div>
                          <a
                            href={`mailto:${user.username}`}
                            className="text-sm sm:text-base text-slate-950 dark:text-slate-50 md:text-slate-50"
                          >
                            {user.username}
                          </a>
                        </div>
                      </li>

                      <li className="flex items-start space-x-3 rtl:space-x-reverse">
                        <div className="flex-none text-xl text-slate-950 dark:text-slate-300 md:text-slate-300">
                          <Icon icon="heroicons:phone-arrow-up-right" />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-slate-950 dark:text-slate-300 md:text-slate-300 uppercase">
                            PHONE
                          </div>
                          <a
                            href={`tel:${user.phone}`}
                            className="text-sm sm:text-base text-slate-600 dark:text-slate-50"
                          >
                            {user.phone}
                          </a>
                        </div>
                      </li>
                    </ul>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Service Package Card */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="md:max-w-[65vw] xl:max-w-[50vw] mx-auto"
        >
          {/* Header */}
          <div className="flex mb-6 items-center justify-center px-6 pt-6 border-b border-slate-100 dark:border-slate-700">
            <h4 className="text-2xl mb-0 font-bold text-slate-900 dark:text-white tracking-tight">{t("package.current_package")}</h4>
          </div>

          <div className="p-0 md:p-6">
            {/* Alert Notice */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="bg-orange-50 border border-orange-100 rounded-lg p-4 mb-6 shadow-sm hidden"
            >
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 text-orange-400">
                  <Icon icon="heroicons:exclamation-triangle" className="w-5 h-5" />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-orange-700">
                    Bạn đang có yêu cầu đổi gói dịch vụ Startup (Theo tháng), vui lòng thanh toán hóa đơn để kích hoạt
                  </p>
                  <button className="mt-2 px-4 py-2 bg-orange-100 text-orange-700 rounded-md text-sm hover:bg-orange-200 transition-colors">
                    Thanh toán ngay
                  </button>
                </div>
              </div>
            </motion.div>

            {/* Service Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-12 gap-8">
              {/* Left Column */}
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="md:col-span-7 shadow-lg"
              >
                <CurrentPackage currentPackage={currentPackage} />
              </motion.div>

              {/* Right Column */}
              <motion.div
                initial={{ opacity: 0, x: 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5, duration: 0.5 }}
                className="md:col-span-5 space-y-4"
              >
                <div className="bg-white dark:dark:bg-slate-700 shadow-xl rounded-lg p-6 hover:shadow-2xl transition-shadow duration-300">
                  <h5 className="font-medium text-xl text-slate-900 dark:text-slate-200 mb-4">{t("package.credit_used")}</h5>
                  <div className="space-y-6">
                    <div>
                      <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 mb-2">
                        <motion.div
                          className="bg-blue-500 h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${((currentPackage?.currentUsage?.creditsUsed || 0) / (currentPackage?.creditLimit || 1) * 100)}%` }}
                          transition={{ duration: 1 }}
                        ></motion.div>
                      </div>
                      <div className="flex justify-between text-sm text-slate-500 dark:text-slate-400">
                        <div className="flex flex-col">
                          <span className="text-[#209BCF] text-base font-semibold">{currentPackage?.currentUsage?.creditsUsed || 0}</span>
                          <span>{t('used')}</span>
                        </div>
                        <div className="flex flex-col">
                          <span className="text-[#209BCF] text-base font-semibold">{currentPackage?.creditLimit || 0}</span>
                          <span>{t('limit')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {((currentPackage?.currentUsage?.creditsUsed || 0) / (currentPackage?.creditLimit || 1) * 100) == 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                    className="bg-white dark:dark:bg-slate-700 shadow-xl rounded-lg p-6"
                  >
                    <div className="flex flex-col items-center text-center space-y-4">
                      <div className="text-amber-500 text-lg font-medium">
                        {t('package.credit_warning')}
                      </div>
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        className="ml-3 mb-6 w-full px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-800 text-white font-bold rounded-full shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-400"
                        animate={{
                          background: [
                            'linear-gradient(to right, #3B82F6, #2563EB)',
                            'linear-gradient(to right, #2563EB, #1D4ED8)',
                            'linear-gradient(to right, #1D4ED8, #7C3AED)',
                            'linear-gradient(to right, #7C3AED, #2563EB)',
                            'linear-gradient(to right, #2563EB, #3B82F6)',
                            'linear-gradient(to right, #3B82F6, #059669)',
                            'linear-gradient(to right, #059669, #2563EB)',
                            'linear-gradient(to right, #2563EB, #3B82F6)',
                          ]
                        }}
                        transition={{
                          duration: 8,
                          repeat: Infinity,
                          repeatType: "reverse",
                          ease: "linear"
                        }}
                        onClick={handleBuyMoreCredits}
                      >
                        {t('package.buy_more_credits')}
                      </motion.button>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default profile;
