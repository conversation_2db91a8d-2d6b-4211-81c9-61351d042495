import React, { useState, useEffect } from 'react';
import { useGetIntegrationMutation, useUpdateIntegrationMutation, useDeleteIntegrationMutation } from "@/store/api/project/integrationSlice";
import { useParams } from "react-router-dom";
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Textinput from '@/components/ui/Textinput';
import Select from '@/components/ui/Select';
import Dropdown from "@/components/ui/Dropdown";
import Icon from "@/components/ui/Icon";
import { Menu } from "@headlessui/react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import UpdateBodyType1 from './updateBodyType/updateBodyType1';
import UpdateBodyType2 from './updateBodyType/updateBodyType2';
import UpdateBodyType3 from './updateBodyType/updateBodyType3';
import UpdateBodyType4 from './updateBodyType/updateBodyType4';
import AutocompleteBotFilter from '../projects/workgate/AutocompleteBotFilter';
import { useLazyGetAllNameQuery } from "@/store/api/project/botApiSlice";
import { useTranslation } from "react-i18next";
import { toast } from "react-toastify";
import { useNavigate } from "react-router-dom";

// Định nghĩa schema dynamic
const getSchema = (type, t) => {
    const baseSchema = {
        description: yup.string().required(t("workgate.integration_name_required")),
        on_chat_bot_uuid: yup.string().required(t("workgate.bot_uuid_required")),
    };
    switch (Number(type)) {
        case 1:
            return yup.object().shape({
                ...baseSchema,
                config: yup.object().shape({
                    other_setting: yup.string(),
                }),
                status: yup.number().oneOf([1, 2, -1]).required(t("workgate.status_required"))
            });
        case 2:
            return yup.object().shape({
                ...baseSchema,
                extraField: yup.string(),
                status: yup.number().oneOf([1, 2, -1]).required(t("workgate.status_required"))
            });
        case 3:
            return yup.object().shape({
                ...baseSchema,
                settings: yup.string(),
                status: yup.number().oneOf([1, 2, -1]).required(t("workgate.status_required"))
            });
        case 4:
            return yup.object().shape({
                ...baseSchema,
                config: yup.object().shape({
                    other_setting: yup.string(),
                }),
                status: yup.number().oneOf([1, 2, -1]).required(t("workgate.status_required"))
            });
        case 6:
            return yup.object().shape({
                ...baseSchema,
                status: yup.number().oneOf([1, 2, -1]).required(t("workgate.status_required"))
            });
        default:
            return yup.object().shape({
                ...baseSchema,
                status: yup.number().oneOf([1, 2, -1]).required(t("workgate.status_required"))
            });
    }
};

// Ánh xạ type và status
const typeMap = {
    1: "Messenger",
    2: "Facebook",
    3: "Twitter",
    4: "Custom Gate",
    6: "Zalo QR",
};

const statusMap = {
    1: "Active",
    2: "Inactive",
    [-1]: "Disabled",
};

const IntegrationDetail = () => {
    const { t } = useTranslation();
    const { id } = useParams();
    const [integration, setIntegration] = useState(null);
    const [notFound, setNotFound] = useState(false);
    const [getIntegration, { isFetching }] = useGetIntegrationMutation();
    const [getBotList] = useLazyGetAllNameQuery();
    const [botList, setBotList] = useState([]);
    const [selectedBot, setSelectedBot] = useState(null);
    const [errorBotSelect, setErrorBotSelect] = useState("");
    const [updateIntegration] = useUpdateIntegrationMutation();
    const [deleteIntegration] = useDeleteIntegrationMutation();
    const navigate = useNavigate();

    useEffect(() => {
        const fetchIntegration = async () => {
            try {
                const res = await getIntegration({ _id: id }).unwrap();
                if (res) {
                    setIntegration(res);
                    setNotFound(false);
                } else {
                    setNotFound(true);
                }
            } catch (error) {
                console.error("Error fetching integration:", error);
                if (error.status === 404 || error.originalStatus === 404) {
                    setNotFound(true);
                } else {
                    toast.error(t("workgate.error_loading_workgate"));
                }
            }
        };
        const fetchBotList = async () => {
            try {
                const res = await getBotList();
                if (res) {
                    setBotList(res.data);
                }
            } catch (error) {
                console.error("Error fetching bot list:", error);
            }
        };

        fetchBotList();
        fetchIntegration();
    }, []);

    const { register, handleSubmit, formState: { errors }, setValue, reset } = useForm({
        resolver: yupResolver(getSchema(integration?.type, t)),
        defaultValues: {},
    });

    useEffect(() => {
        if (integration) {
            reset({
                description: integration.description,
                config: {
                    other_setting: integration.config?.other_setting || "", // Nested config
                },
                status: integration.status,
                on_chat_bot_uuid: integration.config?.on_chat_bot_uuid || "",
            });
            const config = integration.config || {};
            const botUuid = config.on_chat_bot_uuid;
            const defaultBot = botList.find(bot => bot._id === botUuid);
            if (defaultBot) setSelectedBot(defaultBot);
        }
    }, [integration, reset, botList]);

    const getFormattedData = (data) => {
        switch (Number(integration.type)) {
            case 1:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                        other_setting: data.config.other_setting,
                        avatar: data.config.avatar,
                        on_feed_events: data.config.on_feed_events,
                    }),
                    status: data.status,
                };
            case 2:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                        other_setting: data.config.other_setting,
                        avatar: data.config.avatar,
                    }),
                    status: data.status,
                };
            case 3:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                        other_setting: data.config.other_setting,
                        avatar: data.config.avatar,
                    }),
                    status: data.status,
                };
            case 4:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                        other_setting: data.config.other_setting,
                        avatar: data.config.avatar,
                    }),
                    status: data.status,
                };
            case 6:
                return {
                    _id: integration._id,
                    description: data.description,
                    config: JSON.stringify({
                        on_chat_bot_uuid: selectedBot._id,
                    }),
                    status: data.status,
                };
            default:
                return {};
        }
    };

    // const onSubmit = async (data) => {
    //     const formattedData = getFormattedData(data);
    //     console.log("/n/n chạy onSubmit");
    //     if (!selectedBot) {
    //         setErrorBotSelect("Chọn 1 bot");
    //         return;
    //     }

    //     setErrorBotSelect("");
    //     try {
    //         await updateIntegration(formattedData).unwrap();
    //         toast.success("Cập nhật thành công!");
    //     } catch (error) {
    //         console.error("❌ Lỗi khi cập nhật:", error);
    //     }
    // };

    const onSubmit = async (data, e) => {
        e.preventDefault();
        console.log("onSubmit tại IntegrationDetail.jsx >>>>>>>>>>>>> ", data);
        try {
            if (!selectedBot) {
                setErrorBotSelect(t("workgate.please_select_bot"));
                return;
            }

            // Kiểm tra on_feed_events
            if (data?.config?.on_feed_events) {
                const feedEvents = data.config.on_feed_events;

                // validate từng cái feedEvent một
                const hasInvalidEvent = Object.entries(feedEvents).some(([key, event]) => {
                    // if (key === 'undefined') {
                    //     return true;
                    // }

                    // Kiểm tra các giá trị bắt buộc
                    return !event.bot_uuid || !event.name || event.bot_uuid === 'undefined' ||
                        event.name === 'undefined' || event.disabled === undefined;
                });

                if (hasInvalidEvent) {
                    toast.error(t("workgate.invalid_feed_event"));
                    return;
                }
            }

            const formattedData = getFormattedData(data);
            if (!formattedData || Object.keys(formattedData).length === 0) {
                throw new Error(t("workgate.invalid_data"));
            }

            setErrorBotSelect("");
            await updateIntegration(formattedData).unwrap();
            toast.success(t("update_success"));
        } catch (error) {
            console.error("❌ Lỗi khi cập nhật:", error);
            toast.error(error.message || t("workgate.update_error"));
        }
    };

    const onDelete = async (id) => {
        try {
            await deleteIntegration({ _id: id }).unwrap();
            toast.success(t("delete_success"));
            navigate("/integration");
        } catch (error) {
            console.error("❌ Lỗi khi xóa:", error);
            toast.error(t("delete_error"));
        }
    };

    const handleSelectBot = (bot) => {
        setErrorBotSelect("");
        setSelectedBot(bot);
        setValue("on_chat_bot_uuid", bot._id);
    };

    const getBodyComponent = () => {
        switch (Number(integration?.type)) {
            case 1:
                return <UpdateBodyType1 register={register} errors={errors} setValue={setValue} integration={integration} botList={botList} />;
            case 2:
                return <UpdateBodyType2 register={register} errors={errors} setValue={setValue} />;
            case 3:
                return <UpdateBodyType3 register={register} errors={errors} setValue={setValue} />;
            case 4:
                return <UpdateBodyType4 register={register} errors={errors} setValue={setValue} integration={integration} />;
            default:
                return null;
        }
    };

    // Lấy tên bot từ UUID
    const getBotName = () => {
        const config = integration?.config || {};
        const botUuid = selectedBot?._id || config.on_chat_bot_uuid;
        const bot = botList?.find(b => b._id === botUuid);
        return bot ? bot.name : t("workgate.no_bot_selected");
    };

    const workGateCode = `<script>
	some_config_later = {
		_id: "${integration?._id}",
		workgate_name: "${integration?.description}",
		secret: "${integration?.secret}",
	};
</script>`;

    // Handle not found case
    if (notFound) {
        return (
            <div className="container">
                <Card>
                    <div className="text-center py-12">
                        <Icon icon="heroicons-outline:exclamation-triangle" className="w-16 h-16 text-warning-500 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">
                            {t("workgate.not_found_title")}
                        </h3>
                        <p className="text-slate-500 dark:text-slate-400 mb-6">
                            {t("workgate.not_found_description")}
                        </p>
                        <Button
                            text={t("workgate.back_to_list")}
                            className="btn-primary"
                            onClick={() => navigate("/integration")}
                        />
                    </div>
                </Card>
            </div>
        );
    }

    return (
        <div className="container">
            <Card>
                <div className="flex flex-row justify-between mb-5">
                    <h4 className="">{t("workgate.title")}: {integration?.description}</h4>
                    <Dropdown label={
                        <Icon icon="heroicons-outline:adjustments-horizontal" className="w-6 h-6" />
                    } classMenuItems="w-[180px] top-[28px]">
                        <Menu.Item>
                            <div
                                className={`text-slate-600 dark:text-slate-300 hover:text-red-500 block cursor-pointer overflow-hidden`}
                            >
                                <div className={`block cursor-pointer px-4 py-2`} onClick={() => onDelete(integration?._id)}>
                                    <div className="flex items-center">
                                        <span className="block text-xl ltr:mr-3 rtl:ml-3">
                                            <Icon icon="heroicons-outline:trash" />
                                        </span>
                                        <span className="block text-sm">{t("delete")}</span>
                                    </div>
                                </div>
                            </div>
                        </Menu.Item>
                    </Dropdown>
                </div>
                {isFetching ? (
                    <div className="text-center py-8">
                        <Icon icon="heroicons-outline:refresh" className="w-8 h-8 animate-spin text-primary-500 mx-auto mb-2" />
                        <p>{t("workgate.loading")}</p>
                    </div>
                ) : (
                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                        {/* General Info Section */}
                        <Card title={t("workgate.general_info")} className="mb-6">
                            <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
                                {/* Type */}
                                <div className="space-y-2">
                                    <Textinput
                                        label={t("workgate.type")}
                                        type="text"
                                        placeholder={typeMap[integration?.type] || 'Unknown'}
                                        disabled
                                        className="bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300"
                                    />
                                </div>

                                {/* Name */}
                                <div className="space-y-2">
                                    <Textinput
                                        label={t("workgate.name")}
                                        type="text"
                                        name="description"
                                        register={register}
                                        placeholder={t("workgate.enter_workgate_name")}
                                        error={errors.description}
                                    />
                                </div>

                                {/* Status */}
                                <div className="space-y-2">
                                    <Select
                                        label={t("workgate.status")}
                                        name="status"
                                        register={register}
                                        error={errors.status}
                                        options={[
                                            { value: 1, label: t("workgate.active") },
                                            { value: 2, label: t("workgate.inactive") },
                                            { value: -1, label: t("workgate.disabled") }
                                        ]}
                                        placeholder={t("workgate.select_status")}
                                    />
                                    <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                                        {t("workgate.status_active_desc")}<br />
                                        {t("workgate.status_inactive_desc")}
                                    </p>
                                </div>
                            </div>
                        </Card>

                        {/* Event Info Section */}
                        <Card title={t("workgate.event_info")} className="mb-6">
                            <div className="space-y-4">
                                {/* Bot Selection */}
                                <div className="space-y-2">
                                    <label className="form-label">
                                        {t("workgate.messaging_event")}
                                    </label>
                                    <div className="space-y-3">
                                        <div className="flex items-center space-x-2">
                                            <p
                                                className="flex text-base text-primary-500 font-semibold cursor-pointer hover:text-primary-600 transition-colors"
                                                onClick={() => {
                                                    if (selectedBot) {
                                                        window.open(`/workbots/${selectedBot._id}`, '_blank');
                                                    }
                                                }}
                                                title={selectedBot ? t("workgate.click_to_open_bot_settings") : ""}
                                            >
                                                {getBotName()}
                                                {selectedBot && (
                                                    <Icon
                                                        icon="heroicons-outline:external-link"
                                                        className="w-4 h-4 text-primary-500 cursor-pointer hover:text-primary-600 transition-colors center"
                                                        onClick={() => {
                                                            window.open(`/workbots/${selectedBot._id}`, '_blank');
                                                        }}
                                                        title={t("workgate.open_bot_settings")}
                                                    />
                                                )}
                                            </p>
                                        </div>
                                        <AutocompleteBotFilter
                                            botList={botList}
                                            onSelectBot={handleSelectBot}
                                            error={errorBotSelect}
                                            defaultBotUuid={integration?.config?.on_chat_bot_uuid}
                                        />
                                        {errorBotSelect && (
                                            <p className="mt-1 text-sm text-danger-500">{errorBotSelect}</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </Card>

                        {/* Additional Configuration */}
                        {getBodyComponent()}

                        {/* Submit Button */}
                        <div className="flex justify-end">
                            <Button
                                type="submit"
                                text={t("update")}
                                className="btn-primary"
                            />
                        </div>
                    </form>
                )}
            </Card>
        </div>
    );
};

export default IntegrationDetail;
