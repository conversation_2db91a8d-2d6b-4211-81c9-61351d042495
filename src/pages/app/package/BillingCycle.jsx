import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from 'react-toastify';
import { useLazyGetMeQuery } from '@/store/api/auth/authApiSlice';
import { useLazyGetPackageDetailQuery } from '@/store/api/package/packageApiSlice';
import orderService from '@/store/api/user/order-user.service';
import { motion } from "framer-motion";
import { useSelector, useDispatch } from "react-redux";
import { clearPackageId } from '@/store/api/package/packageSelectionSlice';
import { useTranslation } from 'react-i18next';

const bankName = import.meta.env.VITE_BANK_NAME || "MBBank";
const accountNumber = import.meta.env.VITE_BANK_ACCOUNT || "";
const accountHolder = import.meta.env.VITE_BANK_ACCOUNT_HOLDER || "CONG TY TNHH BIZINO";

const Billing = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [getMe] = useLazyGetMeQuery();
  const [getPackageDetail] = useLazyGetPackageDetailQuery();
  const dispatch = useDispatch();
  const packageId = useSelector(state => state.packageSelection.packageId);
  const typeBilling = useSelector(state => state.packageSelection.type);
  // States
  const [isLoading, setIsLoading] = useState(true);
  const [packageInfo, setPackageInfo] = useState(null);
  const [orderId, setOrderId] = useState("");
  const [transferContent, setTransferContent] = useState("");
  const [qrCodeUrl, setQrCodeUrl] = useState("");
  const [paymentStatus, setPaymentStatus] = useState("pending");

  // Redirect if no packageId
  useEffect(() => {
    if (!packageId) {
      navigate('/order-user');
    }
  }, [packageId]);

  // Step 1: Get package info after user info is loaded
  useEffect(() => {
    if (!packageId) return;
    const fetchPackageInfo = async () => {
      try {
        const response = await getPackageDetail(packageId).unwrap();
        setPackageInfo(response);
      } catch (error) {
        console.error("Error fetching package info:", error);
        toast.error(t('billing.cannot_get_package_info'));
        setIsLoading(false);
      }
    };
    fetchPackageInfo();
  }, [packageId, getPackageDetail]);

  // Step 2: Create order after package info is loaded
  useEffect(() => {
    if (!packageInfo) return;
    const createOrder = async () => {
      try {
        // Fetch user info 
        const userResponse = await getMe().unwrap();
        // Determine price based on conditions
        const orderData = {
          user_id: userResponse.teamId || userResponse._id,
          package_id: packageInfo._id,
          amount: packageInfo?.price || 5000,
          affiliate_code: "",
          coupon: "",
          discount_amount: 0,
          package: packageInfo,
        };
        const response = await orderService.createOrder(orderData);
        setOrderId(response.data._id);
        const content = response.data.order_code || "";
        setTransferContent(content);
        const qrUrl = `https://qr.sepay.vn/img?bank=${bankName}&acc=${accountNumber}&template=compact&amount=${packageInfo?.price|| 5000}&des=${content}`;
        setQrCodeUrl(qrUrl);
        setIsLoading(false);
      } catch (error) {
        toast.error(t('billing.cannot_create_order'));
        navigate("/package");
        setIsLoading(false);
      }
    };
    createOrder();
  }, [packageInfo]);

  // Step 3: Check payment status after order is created
  useEffect(() => {
    if (!orderId) return;
    const checkPaymentStatus = async () => {
      try {
        const response = await orderService.getOrderById(orderId);
        if (response.data.status === "completed") {
          setPaymentStatus("completed");
          toast.success(t('billing.payment_success'));
          dispatch(clearPackageId());
          setTimeout(() => {
            navigate("/order-user");
          }, 4000);
        }
      } catch (error) {
        console.error("Error checking payment status:", error);
      }
    };
    const interval = setInterval(checkPaymentStatus, 5000);
    return () => clearInterval(interval);
  }, [orderId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!packageInfo) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {t('billing.package_not_found')}
          </h2>
          <p className="text-gray-600">{t('billing.try_again_later')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-5xl lg:max-w-6xl mx-auto px-2 sm:px-4 lg:px-8">
        <motion.div
          className="bg-white rounded-xl p-3 sm:p-6 lg:p-8 shadow-sm border border-gray-100"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 gap-2">
            <h2 className="text-xl sm:text-2xl font-semibold">{t('billing.payment')}</h2>
            <div className="flex items-center space-x-2">
              {paymentStatus === "pending" ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                  <span className="px-4 py-1.5 bg-blue-50 text-blue-600 rounded-full text-sm font-medium">
                    {t('billing.pending_payment')}
                  </span>
                </>
              ) : (
                <>
                  <svg
                    className="w-5 h-5 text-green-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  <span className="px-4 py-1.5 bg-green-50 text-green-600 rounded-full text-sm font-medium">
                    {t('billing.payment_success')}
                  </span>
                </>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-12 mb-6 sm:mb-8">
            <motion.div
              className="space-y-3 sm:space-y-4"
              initial={{ opacity: 0, x: -40 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <h3 className="text-base sm:text-lg font-medium">{t('billing.package_info')}</h3>
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                <div className="flex justify-between items-start mb-2">
                  <span className="text-gray-600">{t('billing.service_package')}</span>
                  <span className="text-gray-900 font-medium">
                    {packageInfo.name}
                  </span>
                </div>
                <div className="flex justify-between items-start">
                  <span className="text-gray-600">{t('billing.amount')}</span>
                  <span className="text-blue-600 font-bold">
                    {packageInfo?.price?.toLocaleString()}
                    {t('billing.currency')}
                  </span>
                </div>
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    {t('billing.features')}:
                  </h4>
                  <ul className="space-y-2">
                    <li className="flex items-center text-sm text-gray-600">
                      <svg
                        className="w-4 h-4 text-green-500 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      {t('billing.knowledge_limit')}: {packageInfo.knowledgeLimit}
                    </li>
                    <li className="flex items-center text-sm text-gray-600">
                      <svg
                        className="w-4 h-4 text-green-500 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      {t('billing.member_limit')}: {packageInfo.memberLimit}
                    </li>
                    <li className="flex items-center text-sm text-gray-600">
                      <svg
                        className="w-4 h-4 text-green-500 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      {t('billing.credit_limit')}: {packageInfo.creditLimit}
                    </li>
                    <li className="flex items-center text-sm text-gray-600">
                      <svg
                        className="w-4 h-4 text-green-500 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      {t('billing.bot_limit')}: {packageInfo.botSettingLimit}
                    </li>
                    <li className="flex items-center text-sm text-gray-600">
                      <svg
                        className="w-4 h-4 text-green-500 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      {t('billing.connector_limit')}: {packageInfo.connectorLimit}
                    </li>
                  </ul>
                </div>
              </div>
            </motion.div>
            <motion.div
              className="space-y-3 sm:space-y-4"
              initial={{ opacity: 0, x: 40 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.5 }}
            >
              <h3 className="text-base sm:text-lg font-medium">{t('billing.transfer_info')}</h3>
              <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                <div className="flex flex-col items-center sm:flex-row sm:justify-between sm:items-center mb-2 gap-1">
                  <span className="text-gray-600 w-full text-center sm:w-auto sm:text-left">{t('billing.bank')}</span>
                  <span className="text-gray-900 font-medium w-full text-center sm:w-auto sm:text-right">{bankName}</span>
                </div>
                <div className="flex flex-col items-center sm:flex-row sm:justify-between sm:items-center mb-2 gap-1">
                  <span className="text-gray-600 w-full text-center sm:w-auto sm:text-left">{t('billing.account_number')}</span>
                  <span className="text-gray-900 font-medium w-full text-center sm:w-auto sm:text-right">{accountNumber}</span>
                </div>
                <div className="flex flex-col items-center sm:flex-row sm:justify-between sm:items-center mb-2 gap-1">
                  <span className="text-gray-600 w-full text-center sm:w-auto sm:text-left">{t('billing.account_holder')}</span>
                  <span className="text-gray-900 font-medium w-full text-center sm:w-auto sm:text-right">{accountHolder}</span>
                </div>
                <div className="flex flex-col items-center sm:flex-row sm:justify-between sm:items-center gap-1">
                  <span className="text-gray-600 w-full text-center sm:w-auto sm:text-left">{t('billing.content')}</span>
                  <span className="text-gray-900 font-medium break-all w-full text-center sm:w-auto sm:text-right">{transferContent}</span>
                </div>
              </div>
            </motion.div>
          </div>

          <motion.div
            className="border-t border-gray-100 pt-4 sm:pt-6"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4 text-center">
              {t('billing.scan_qr')}
            </h3>
            {qrCodeUrl && (
              <div className="flex flex-col items-center">
                <motion.div
                  className="bg-white p-2 sm:p-4 rounded-lg shadow-sm border border-gray-100"
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <img src={qrCodeUrl} alt="QR Code" className="w-full max-w-xs sm:w-64 sm:h-64" />
                </motion.div>
                <p className="text-xs sm:text-sm text-gray-500 mt-2 sm:mt-4 text-center">
                  {t('billing.scan_qr_instruction')}
                </p>
              </div>
            )}
          </motion.div>

          <div className="mt-6 sm:mt-8 text-center">
            <p className="text-xs sm:text-sm text-gray-500">
              {t('billing.redirect_notice')}
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Billing;
