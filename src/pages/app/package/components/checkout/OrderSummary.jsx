import React, { useState } from "react";
import { useTranslation } from 'react-i18next';

const OrderSummary = ({ packageInfo, currentTeamPackage, creditAmount, type = "package" }) => {
  const { t } = useTranslation();
  const [couponCode, setCouponCode] = useState('');
  const [couponError, setCouponError] = useState('');

  // Tính toán số tiền còn lại của gói cũ
  const calculateRemainingAmount = () => {
    if (!currentTeamPackage || !packageInfo) return null;

    const currentPackagePrice = currentTeamPackage.price || 0;
    const currentPackageCredits = currentTeamPackage.creditLimit;
    const creditsUsed = currentTeamPackage.currentUsage?.creditsUsed || 0;
    
    // Tính giá trị của mỗi credit
    const creditValue = currentPackagePrice / currentPackageCredits;
    
    // T<PERSON>h số credit còn lại
    const remainingCredits = currentPackageCredits - creditsUsed;
    
    // Tính số tiền còn lại
    const remainingAmount = remainingCredits * creditValue;
    
    // Tính số tiền phải thanh toán (giá gói mới - số tiền còn lại)
    const finalAmount = Math.max(0, packageInfo.price - remainingAmount);
    
    return {
      remainingAmount,
      finalAmount,
      remainingCredits
    };
  };

  const handleApplyCoupon = () => {
    // Reset error
    setCouponError('');

    // Validate coupon code
    if (!couponCode.trim()) {
      setCouponError(t('checkout.coupon_required'));
      return;
    }
    //Tất cả các coupon hiện tại đều không hợp lệ do chưa có chức năng
    if (couponCode) {
      setCouponError(t('checkout.coupon_invalid'));
      return;
    }

    // TODO: Implement coupon validation logic
    console.log('Applying coupon:', couponCode);
  };

  const renderCouponSection = () => (
    <div className="border-t pt-4">
      <div className="space-y-2">
        <div className="flex gap-2">
          <input
            type="text"
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
            placeholder={t('checkout.enter_coupon')}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={handleApplyCoupon}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {t('checkout.apply')}
          </button>
        </div>
        {couponError && (
          <p className="text-red-500 text-sm">{couponError}</p>
        )}
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-sm border border-gray-100">
      <h2 className="text-lg font-semibold mb-4">{t('checkout.order_info')}</h2>
      {currentTeamPackage ? (
        // Trường hợp đổi gói
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">{t('checkout.current_package')}</span>
            <span className="font-medium">{currentTeamPackage.packageName}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">{t('checkout.remaining_credits')}</span>
            <span className="font-medium">{calculateRemainingAmount()?.remainingCredits || 0}</span>
          </div>
          {currentTeamPackage.price && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">{t('checkout.remaining_amount')}</span>
              <span className="font-medium text-green-500">{calculateRemainingAmount()?.remainingAmount?.toLocaleString('vi-VN')} {t('checkout.currency')}</span>
            </div>
          )}
          <div className="border-t pt-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">{t('checkout.new_package')}</span>
              <span className="font-medium">{packageInfo.name}</span>
            </div>
            <div className="flex justify-between items-center mt-2">
              <span className="text-gray-600">{t('checkout.package_price')}</span>
              <span className="font-medium">{packageInfo.price?.toLocaleString('vi-VN')} {t('checkout.currency')}</span>
            </div>
          </div>

          {type !== "credit" && renderCouponSection()}

          <div className="border-t pt-4">
            <div className="flex justify-between items-center font-semibold">
              <span>{t('checkout.final_amount')}</span>
              <span className="text-blue-600">{calculateRemainingAmount()?.finalAmount?.toLocaleString('vi-VN')} {t('checkout.currency')}</span>
            </div>
          </div>
        </div>
      ) : (
        // Trường hợp mua mới
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-gray-600">{t('checkout.package')}</span>
            <span className="font-medium">{packageInfo.name}</span>
          </div>

          {type !== "credit" && renderCouponSection()}
            
          {type === "credit" && (
            <div className="flex justify-between items-center">
              <span className="text-gray-600">{t('checkout.credit_amount')}</span>
              <span className="font-medium text-green-500">{creditAmount.toLocaleString('vi-VN')}</span>
            </div>
          )}
          
          <div className="border-t pt-4">
            <div className="flex justify-between items-center font-semibold">
              <span>{t('checkout.total')}</span>
              <span className="text-blue-600">{packageInfo.price?.toLocaleString('vi-VN')} {t('checkout.currency')}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderSummary; 