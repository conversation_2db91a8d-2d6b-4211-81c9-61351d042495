import React from "react";
import { useTranslation } from 'react-i18next';

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("vi-VN", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

const CurrentPackageInfo = ({ currentTeamPackage }) => {
  const { t } = useTranslation();
  if (!currentTeamPackage) return null;
  return (
    <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-sm border border-gray-100 mt-6 mb-6">
      <div className="inline-block px-4 py-1.5 bg-blue-50 text-blue-600 rounded-full text-sm font-medium mb-4">
        {t('checkout.current_package')}
      </div>
      <div className="space-y-4">
        <div className="flex justify-between items-start">
          <span className="text-gray-900 text-lg font-medium">
            {currentTeamPackage.packageName}
          </span>
          <span className="text-gray-900 font-medium">
            {currentTeamPackage.price
              ? currentTeamPackage.price.toLocaleString() + t('checkout.currency')
              : ""}
          </span>
        </div>
        <div className="border-t border-gray-100 pt-4">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <p className="text-sm mb-2 text-gray-500">{t('checkout.start_date')}</p>
              <p className="text-gray-900 font-medium">
                {formatDate(currentTeamPackage.startDate)}
              </p>
            </div>
            <div>
              <p className="text-sm mb-2 text-gray-500">{t('checkout.expiration_date')}</p>
              <p className="text-gray-900 font-medium">
                {formatDate(currentTeamPackage.expirationDate)}
              </p>
            </div>
             <div>
              <p className="text-sm mb-2 text-gray-500">{t('checkout.credit_limit')}</p>
              <p className="text-gray-900 font-medium">
                {currentTeamPackage.creditLimit?.toLocaleString('vi-VN') || 0}
              </p>
            </div>
            <div>
              <p className="text-sm mb-2 text-gray-500">{t('checkout.credits_used')}</p>
              <p className="text-gray-900 font-medium">
                {currentTeamPackage.currentUsage?.creditsUsed?.toLocaleString('vi-VN') || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="py-2 flex items-center justify-center">
        <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center">
          <svg
            className="w-4 h-4 text-blue-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default CurrentPackageInfo; 