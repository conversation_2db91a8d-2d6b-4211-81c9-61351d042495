import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useCurrentQuery } from '@/store/api/teamPackage/teamPackageApiSlice';
import { useTranslation } from 'react-i18next';
import CurrentPackageInfo from "../package/components/checkout/CurrentPackageInfo";
import OrderSummary from "../package/components/checkout/OrderSummary";
import TermsAndContinue from "../package/components/checkout/TermsAndContinue";
import { useDispatch } from "react-redux";
import { setType } from "@/store/api/package/packageSelectionSlice";

const CREDIT_UNIT_PRICE = 1000; // giá một credit 
const MIN_CREDIT = 10000;
const MAX_CREDIT = 1000000;

const CreditCheckOut = () => {
  const navigate = useNavigate();
  const { data: currentPackage } = useCurrentQuery();
  const { t } = useTranslation();
  const [creditAmount, setCreditAmount] = useState(MIN_CREDIT);
    const [totalPrice, setTotalPrice] = useState(CREDIT_UNIT_PRICE * MIN_CREDIT);
    

  const dispatch = useDispatch();
  useEffect(() => {
    setTotalPrice(creditAmount * CREDIT_UNIT_PRICE);
  }, [creditAmount]);

  const handleSliderChange = (e) => {
    setCreditAmount(Number(e.target.value));
  };

  const handleInputChange = (e) => {
    let value = Number(e.target.value);
    if (isNaN(value)) value = MIN_CREDIT;
    if (value < MIN_CREDIT) value = MIN_CREDIT;
    if (value > MAX_CREDIT) value = MAX_CREDIT;
    setCreditAmount(value);
  };

  const handleContinue = () => {
    dispatch(setType("credit"));
    navigate('/package/billing');
      
  };

  return (
    <div className="min-h-fit pb-6 bg-gray-50">
      <div className="max-w-5xl lg:max-w-6xl mx-auto px-2 sm:px-4 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-12">
          {/* Left Column */}
          <div className="space-y-6 lg:col-span-7">
            {/* Current Package Info */}
            {currentPackage && (
              <CurrentPackageInfo currentTeamPackage={currentPackage} />
            )}

            {/* Credit Selector */}
            <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-sm border border-gray-100">
              <h2 className="text-lg font-semibold mb-4">{t('package.buy_more_credits')}</h2>
              <div className="flex flex-col gap-4">

                <div className="relative w-full flex items-center mt-8">
                  <input
                    type="range"
                    min={MIN_CREDIT}
                    max={MAX_CREDIT}
                    step={10}
                    value={creditAmount}
                    onChange={handleSliderChange}
                    className="w-full h-2 bg-blue-200 rounded-lg appearance-none accent-blue-500 focus:outline-none transition-all duration-300"
                  />
                
                  <div
                    className="absolute -top-10 left-0 transform -translate-x-1/2"
                    style={{ left: `calc(${((creditAmount-MIN_CREDIT)/(MAX_CREDIT-MIN_CREDIT))*100}% )` }}
                  >
                    <div className="px-2 py-2 bg-blue-500 text-white rounded text-xs font-semibold shadow">
                      {creditAmount.toLocaleString('vi-VN')}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <button
                    onClick={() => setCreditAmount(Math.max(MIN_CREDIT, creditAmount-MIN_CREDIT))}
                    className="w-8 h-8 flex items-center justify-center rounded bg-blue-100 text-blue-600 hover:bg-blue-200 text-base transition"
                  >-</button>
                  <input
                    type="number"
                    min={MIN_CREDIT}
                    max={MAX_CREDIT}
                    value={creditAmount}
                    onChange={handleInputChange}
                    className="w-36 px-2 py-1 border border-blue-200 rounded text-base text-center focus:ring-1 focus:ring-blue-400 focus:border-blue-400 transition"
                  />
                  <button
                    onClick={() => setCreditAmount(Math.min(MAX_CREDIT, creditAmount+MIN_CREDIT))}
                    className="w-8 h-8 flex items-center justify-center rounded bg-blue-100 text-blue-600 hover:bg-blue-200 text-base transition"
                  >+</button>
              
                </div>
              
              </div>
            </div>
          </div>

          {/* Right Column - Order Summary */}
          <div className="lg:col-span-5 sticky pt-6 top-[6.5rem] h-fit">
            <OrderSummary 
              packageInfo={{
                ...currentPackage,
                name: t('package.buy_more_credits'),
                price: totalPrice,
              }}
              currentTeamPackage={null} // Không cần so sánh gói khi chỉ mua credit
              creditAmount={creditAmount}
              type="credit"           
            />
            <div className="space-y-4">
              <TermsAndContinue onContinue={handleContinue} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreditCheckOut; 