import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useCurrentQuery } from '@/store/api/teamPackage/teamPackageApiSlice';
import { useLazyGetPackageDetailQuery } from '@/store/api/package/packageApiSlice';
import { toast } from 'react-toastify';
import { useSelector } from "react-redux";
import { useTranslation } from 'react-i18next';

import CurrentPackageInfo from "../package/components/checkout/CurrentPackageInfo";
import NewPackageInfo from "../package/components/checkout/NewPackageInfo";
import PaymentMethod from "../package/components/checkout/PaymentMethod";
import InvoiceSection from "../package/components/checkout/InvoiceSection";
import OrderSummary from "../package/components/checkout/OrderSummary";
import TermsAndContinue from "../package/components/checkout/TermsAndContinue";

const Checkout = () => {
  const navigate = useNavigate();
  const { data: currentPackage, isLoading: isLoadingCurrentPackage } = useCurrentQuery();
  const [getPackageDetail] = useLazyGetPackageDetailQuery();
  const packageId = useSelector(state => state.packageSelection.packageId);
  const [packageInfo, setPackageInfo] = useState(null);
  const [currentTeamPackage, setCurrentTeamPackage] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState("momo");
  const [isLoading, setIsLoading] = useState(true);
  const [needInvoice, setNeedInvoice] = useState(false);
  const [invoiceInfo, setInvoiceInfo] = useState({
    companyName: "",
    taxCode: "",
    address: "",
    email: "",
    phone: "",
    personalName: "",
    personalId: "",
  });
  const [invoiceType, setInvoiceType] = useState("personal");
  const [errors, setErrors] = useState({});
  const { t } = useTranslation();

  // Redirect if no packageId
  useEffect(() => {
    if (!packageId) {
      navigate('/package');
    }
  }, [packageId]);

  // Update current package when data is available
  useEffect(() => {
    if (currentPackage) {
      setCurrentTeamPackage(currentPackage);
    }
  }, [currentPackage]);

  // Get new package info
  useEffect(() => {
    if (!packageId) return;
    const fetchPackageInfo = async () => {
      try {
        const response = await getPackageDetail(packageId).unwrap();
        setPackageInfo(response);
        setIsLoading(false);
      } catch (error) {
        toast.error(t('checkout.cannot_get_package_info'));
        console.error("Error fetching package info:", error);
        setIsLoading(false);
      }
    };
    fetchPackageInfo();
  }, [packageId, getPackageDetail]);

  const handleClickContinute = () => {
    if (needInvoice && !validateForm()) {
      toast.error(t('checkout.invoice_info_invalid'));
      return;
    }
    navigate(`/package/billing`);
  };

  const validateForm = () => {
    const newErrors = {};
    if (invoiceType === "company") {
      if (!invoiceInfo.companyName) {
        newErrors.companyName = t('checkout.company_name_required');
      }
      if (!invoiceInfo.taxCode) {
        newErrors.taxCode = t('checkout.tax_code_required');
      } else if (!/^\d{10,13}$/.test(invoiceInfo.taxCode)) {
        newErrors.taxCode = t('checkout.tax_code_invalid');
      }
    } else {
      if (!invoiceInfo.personalName) {
        newErrors.personalName = t('checkout.personal_name_required');
      }
      if (!invoiceInfo.personalId) {
        newErrors.personalId = t('checkout.personal_id_required');
      } else if (!/^\d{9,12}$/.test(invoiceInfo.personalId)) {
        newErrors.personalId = t('checkout.personal_id_invalid');
      }
    }
    if (!invoiceInfo.email) {
      newErrors.email = t('checkout.email_required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(invoiceInfo.email)) {
      newErrors.email = t('checkout.email_invalid');
    }
    if (!invoiceInfo.phone) {
      newErrors.phone = t('checkout.phone_required');
    } else if (!/^0\d{9}$/.test(invoiceInfo.phone)) {
      newErrors.phone = t('checkout.phone_invalid');
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInvoiceInfoChange = (e) => {
    const { name, value } = e.target;
    setInvoiceInfo((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-5xl lg:max-w-6xl mx-auto px-2 sm:px-4 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-12">
          {/* Left Column */}
          <div className="space-y-6 lg:col-span-7">
            {/* Current Package Info */}
            {currentTeamPackage && (
              <CurrentPackageInfo currentTeamPackage={currentTeamPackage} />
            )}

            {/* New Package Info */}
            {packageInfo && (
              <NewPackageInfo
                packageInfo={packageInfo}
                currentTeamPackage={currentTeamPackage}
              />
            )}

            {/* Payment Method */}
            <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-sm border border-gray-100">
              <PaymentMethod
                paymentMethod={paymentMethod}
                onPaymentMethodChange={setPaymentMethod}
              />
            </div>

            {/* Invoice Section */}
            {/* <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-sm border border-gray-100">
              <div className="space-y-4">
                <InvoiceSection
                  invoiceType={invoiceType}
                  onInvoiceTypeChange={setInvoiceType}
                  invoiceInfo={invoiceInfo}
                  onInvoiceInfoChange={handleInvoiceInfoChange}
                  errors={errors}
                  needInvoice={needInvoice}
                  onNeedInvoiceChange={setNeedInvoice}
                />
              </div>
            </div> */}
          </div>

          {/* Right Column - Order Summary */}
          <div className="lg:col-span-5 sticky top-[6.5rem] h-fit">
            {packageInfo && (
              <OrderSummary 
                packageInfo={packageInfo} 
                currentTeamPackage={currentTeamPackage}
              />
            )}
            {/* Terms and Continue Button */}
            <div className="space-y-4">
              <TermsAndContinue onContinue={handleClickContinute} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
