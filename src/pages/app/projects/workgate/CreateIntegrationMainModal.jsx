import React, { useState, useEffect } from "react";
import Modal from "@/components/ui/Modal";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import "@/assets/scss/components/_chatbox.scss";
import { useTranslation } from "react-i18next";
import FormGroup from "@/components/ui/FormGroup";
import { toast } from "react-toastify";
import Loading from "@/components/Loading";
import { useLazyGetAllNameQuery } from "@/store/api/project/botApiSlice";
import BodyType1 from "./BodyType/BodyType1";
import BodyType2 from "./BodyType/BodyType2";
import BodyType3 from "./BodyType/BodyType3";
import BodyType4 from "./BodyType/BodyType4";
import BodyTypeZaloQR from "./BodyType/BodyTypeZaloQR";
import AutocompleteBotFilter from "./AutocompleteBotFilter";
import facebook from "@/assets/images/platform/facebook.png";
import telegram from "@/assets/images/platform/telegram.png";
import whatsapp from "@/assets/images/platform/whatsapp.png";
import zalo from "@/assets/images/platform/zalo.png";
import tiktok from "@/assets/images/platform/tiktok.png";
import shopee_official from "@/assets/images/platform/shopee_official.png";
import zalo_oa_2 from "@/assets/images/platform/zalo_oa_2.jpg";
import gmail from "@/assets/images/platform/gmail.png";
import icon_email from "@/assets/images/platform/icon_email.png";

const platformIcons = {
    1: facebook,
    2: telegram,
    3: whatsapp,
    4: zalo,
    5: tiktok,
    6: shopee_official,
    7: zalo_oa_2,
    // 8: webhook, // Removed webhook
    9: gmail,
    10: icon_email,
    11: zalo // Zalo QR Code
};

const schema = yup.object().shape({
    description: yup.string(),
    type: yup.number(),
    on_chat_bot_uuid: yup.string(),
    other_setting: yup.string(),
});

const CreateIntegrationMainModal = ({
    bot_uuid,
    openModal = false,
    closeModal,
    bot_name,
    workGateTypes = []
}) => {
    const { t } = useTranslation();
    const [getBotList, { isFetching }] = useLazyGetAllNameQuery();
    const [botList, setBotList] = useState([]);
    const { register, handleSubmit, formState: { errors }, setValue } = useForm({
        resolver: yupResolver(schema),
    });
    const [type, setType] = useState({ value: 1, label: "Messenger" });

    const getBodyComponent = () => {
        switch (type.value) {
            case 1: return <BodyType1
                // register={register}
                errors={errors}
                setValue={setValue}
                botList={botList}
                bot_uuid={bot_uuid}
                closeModal={closeModal}
                type={type}
            />;
            case 2: return <BodyType2 register={register} errors={errors} />;
            case 3: return <BodyType3 register={register} errors={errors} />;
            case 4: return <BodyType4
                setValue={setValue}
                botList={botList}
                bot_uuid={bot_uuid}
                errors={errors}
                closeModal={closeModal}
            />;
            case 5: return <BodyType3 register={register} errors={errors} />;
            case 6: return <BodyType3 register={register} errors={errors} />;
            case 7: return <BodyType3 register={register} errors={errors} />;
            case 8: return <BodyType3 register={register} errors={errors} />;
            case 9: return <BodyType3 register={register} errors={errors} />;
            case 10: return <BodyType3 register={register} errors={errors} />;
            case 11: return <BodyTypeZaloQR
                botList={botList}
                bot_uuid={bot_uuid}
                closeModal={closeModal}
            />;
            default: return null;
        }
    };

    useEffect(() => {
        const fetchBotList = async () => {
            const res = await getBotList();
            console.log("Fetch từ modal");
            if (res) {
                setBotList(res.data);
            }
        };
        fetchBotList();
    }, []);

    const onSubmit = () => {
        // Không cần logic submit tại đây, để BodyType1 xử lý
    };

    const handleTypeChange = (selectedType) => {
        setType(selectedType);
    };

    return (
        <Modal
            title={t("workgate.add_integration")}
            activeModal={openModal}
            onClose={() => closeModal(true)}
            centered
            className="create-integration-modal w-full"
            themeClass="integration-modal bg-slate-900 dark:bg-slate-800 dark:border-b dark:border-slate-700 w-full"
        >
            <div className="w-full flex min-h-[70vh] gap-3 bg-black-500">
                <div className="w-fit  lg:w-1/5 menu h-100 p-3 bg-white rounded-sm">
                    {workGateTypes.filter(item => item.value !== 5 && item.key !== 'Webhook').map((item) => (
                        <div
                            key={item.value}
                            className={`item w-full p-2 rounded-lg mb-2 cursor-pointer ${type.value === item.value
                                ? 'bg-[#eaecf0]'
                                : 'hover:bg-gray-200'
                                }`}
                            onClick={() => handleTypeChange(item)}
                        >
                            <div className="flex items-center">
                                <img
                                    src={platformIcons[item.value]}
                                    alt={item.key}
                                    className="w-5 h-5 me-2 flatform-icon-select"
                                />
                                <span className="hidden lg:flex lg:items-center text-black-600 text-[14px]">
                                    {item.key}
                                </span>
                            </div>
                        </div>
                    ))}
                    {/* Add Zalo QR Code option after existing options */}
                    <div
                        className={`item w-full p-2 rounded-lg mb-2 cursor-pointer ${type.value === 11
                            ? 'bg-[#eaecf0]'
                            : 'hover:bg-gray-200'
                            }`}
                        onClick={() => setType({ value: 11, label: "Zalo QR" })}
                    >
                        <div className="flex items-center">
                            <img
                                src={zalo}
                                alt={11}
                                className="w-5 h-5 min-w-[32px] min-h-[32px] me-2 flatform-icon-select"
                            />
                            <span className="hidden lg:flex lg:items-center text-black-600 text-[14px]">
                                Zalo QR
                            </span>
                        </div>
                    </div>
                    {/* tạm thời showoff */}
                    <div
                        className={`item w-full p-2 rounded-lg mb-2 cursor-pointer ${type.value === 5
                            ? 'bg-[#eaecf0]'
                            : 'hover:bg-gray-200'
                            }`}
                        onClick={() => setType({ value: 5, label: "Tiktok" })}
                    >
                        <div className="flex items-center">
                            <img
                                src={tiktok}
                                alt={5}
                                className="w-5 h-5 me-2 flatform-icon-select"
                            />
                            <span className="hidden lg:flex lg:items-center text-black-600 text-[14px]">
                                Tiktok
                            </span>
                        </div>
                    </div>

                    <div
                        className={`item w-full p-2 rounded-lg mb-2 cursor-pointer ${type.value === 6
                            ? 'bg-[#eaecf0]'
                            : 'hover:bg-gray-200'
                            }`}
                        onClick={() => setType({ value: 6, label: "Shopee" })}
                    >
                        <div className="flex items-center">
                            <img
                                src={shopee_official}
                                alt={6}
                                className="w-5 h-5 me-2 flatform-icon-select"
                            />
                            <span className="hidden lg:flex lg:items-center text-black-600 text-[14px]">
                                Shopee
                            </span>
                        </div>
                    </div>

                    <div
                        className={`item w-full p-2 rounded-lg mb-2 cursor-pointer ${type.value === 7
                            ? 'bg-[#eaecf0]'
                            : 'hover:bg-gray-200'
                            }`}
                        onClick={() => setType({ value: 7, label: "Zalo OA" })}
                    >
                        <div className="flex items-center">
                            <img
                                src={zalo_oa_2}
                                alt={7}
                                className="w-5 h-5 min-w-[32px] min-h-[32px] me-2 flatform-icon-select"
                            />
                            <span className="hidden lg:flex lg:items-center text-black-600 text-[14px]">
                                Zalo OA
                            </span>
                        </div>
                    </div>



                    <div
                        className={`item w-full p-2 rounded-lg mb-2 cursor-pointer ${type.value === 9
                            ? 'bg-[#eaecf0]'
                            : 'hover:bg-gray-200'
                            }`}
                        onClick={() => setType({ value: 9, label: "Gmail" })}
                    >
                        <div className="flex items-center">
                            <img
                                src={gmail}
                                alt={9}
                                className="w-5 h-5 min-w-[32px] min-h-[32px] me-2 flatform-icon-select"
                            />
                            <span className="hidden lg:flex lg:items-center text-black-600 text-[14px]">
                                Gmail
                            </span>
                        </div>
                    </div>

                    <div
                        className={`item w-full p-2 rounded-lg mb-2 cursor-pointer ${type.value === 10
                            ? 'bg-[#eaecf0]'
                            : 'hover:bg-gray-200'
                            }`}
                        onClick={() => setType({ value: 10, label: "Smtp" })}
                    >
                        <div className="flex items-center">
                            <img
                                src={icon_email}
                                alt={10}
                                className="w-5 h-5 min-w-[32px] min-h-[32px] me-2 flatform-icon-select"
                            />
                            <span className="hidden lg:flex lg:items-center text-black-600 text-[14px]">
                                Smtp
                            </span>
                        </div>
                    </div>

                    {/* tạm thời showoff */}
                </div>
                <div className="w-4/5 h-100 bg-white rounded-sm p-2 flex flex-col">
                    {getBodyComponent()}
                </div>
            </div>
        </Modal>
    );
};

export default CreateIntegrationMainModal;